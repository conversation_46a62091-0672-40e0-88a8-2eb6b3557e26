const nodemailer = require('nodemailer');

class EmailService {
    constructor() {
        this.transporter = null;
        this.adminEmail = '<EMAIL>';
        this.initialized = false;
    }

    // Initialize email transporter
    async initializeTransporter() {
        if (this.initialized) return;

        try {
            // For development/testing, create a test account if no SMTP is configured
            if (!process.env.SMTP_USER) {
                await this.createTestAccount();
            } else {
                // For production, use real SMTP credentials
                this.transporter = nodemailer.createTransporter({
                    host: process.env.SMTP_HOST || 'smtp.gmail.com',
                    port: process.env.SMTP_PORT || 587,
                    secure: false, // true for 465, false for other ports
                    auth: {
                        user: process.env.SMTP_USER,
                        pass: process.env.SMTP_PASS
                    }
                });
            }
            this.initialized = true;
        } catch (error) {
            console.error('Failed to initialize email service:', error);
        }
    }

    // Create test account for development
    async createTestAccount() {
        try {
            const testAccount = await nodemailer.createTestAccount();
            
            this.transporter = nodemailer.createTransporter({
                host: 'smtp.ethereal.email',
                port: 587,
                secure: false,
                auth: {
                    user: testAccount.user,
                    pass: testAccount.pass
                }
            });

            console.log('Test email account created:');
            console.log('User:', testAccount.user);
            console.log('Pass:', testAccount.pass);
        } catch (error) {
            console.error('Failed to create test email account:', error);
        }
    }

    // Send booking confirmation email to user
    async sendBookingConfirmation(booking) {
        try {
            await this.initializeTransporter();
            if (!this.transporter) {
                throw new Error('Email transporter not initialized');
            }

            const userEmailContent = this.generateBookingConfirmationEmail(booking);
            
            // Send to user
            const userMailOptions = {
                from: '"Sirens FC Booking System" <<EMAIL>>',
                to: booking.userEmail,
                subject: `Booking Confirmation - ${booking.bookingReference}`,
                html: userEmailContent
            };

            const userResult = await this.transporter.sendMail(userMailOptions);
            console.log('Booking confirmation sent to user:', userResult.messageId);

            // Send notification to admin
            await this.sendBookingNotificationToAdmin(booking);

            return {
                success: true,
                messageId: userResult.messageId,
                previewUrl: nodemailer.getTestMessageUrl(userResult)
            };

        } catch (error) {
            console.error('Failed to send booking confirmation:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Send booking notification to admin
    async sendBookingNotificationToAdmin(booking) {
        try {
            const adminEmailContent = this.generateAdminNotificationEmail(booking);
            
            const adminMailOptions = {
                from: '"Sirens FC Booking System" <<EMAIL>>',
                to: this.adminEmail,
                subject: `New Booking Received - ${booking.bookingReference}`,
                html: adminEmailContent
            };

            const adminResult = await this.transporter.sendMail(adminMailOptions);
            console.log('Admin notification sent:', adminResult.messageId);

            return adminResult;

        } catch (error) {
            console.error('Failed to send admin notification:', error);
            throw error;
        }
    }

    // Send booking cancellation email
    async sendBookingCancellation(booking) {
        try {
            await this.initializeTransporter();
            if (!this.transporter) {
                throw new Error('Email transporter not initialized');
            }

            const userEmailContent = this.generateBookingCancellationEmail(booking);
            
            // Send to user
            const userMailOptions = {
                from: '"Sirens FC Booking System" <<EMAIL>>',
                to: booking.userEmail,
                subject: `Booking Cancelled - ${booking.bookingReference}`,
                html: userEmailContent
            };

            const userResult = await this.transporter.sendMail(userMailOptions);
            console.log('Booking cancellation sent to user:', userResult.messageId);

            // Send notification to admin
            const adminEmailContent = this.generateAdminCancellationEmail(booking);
            
            const adminMailOptions = {
                from: '"Sirens FC Booking System" <<EMAIL>>',
                to: this.adminEmail,
                subject: `Booking Cancelled - ${booking.bookingReference}`,
                html: adminEmailContent
            };

            await this.transporter.sendMail(adminMailOptions);

            return {
                success: true,
                messageId: userResult.messageId,
                previewUrl: nodemailer.getTestMessageUrl(userResult)
            };

        } catch (error) {
            console.error('Failed to send booking cancellation:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Generate booking confirmation email HTML
    generateBookingConfirmationEmail(booking) {
        const formattedDate = new Date(booking.date).toLocaleDateString('en-GB', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #1a365d; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .booking-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                .highlight { color: #1a365d; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Booking Confirmed!</h1>
                    <p>Sirens FC - St. Paul's Bay, Malta</p>
                </div>
                
                <div class="content">
                    <p>Dear ${booking.userFirstName} ${booking.userLastName},</p>
                    
                    <p>Thank you for your booking! Your reservation has been confirmed.</p>
                    
                    <div class="booking-details">
                        <h3>Booking Details</h3>
                        <p><strong>Booking Reference:</strong> <span class="highlight">${booking.bookingReference}</span></p>
                        <p><strong>Facility:</strong> ${booking.facilityName}</p>
                        <p><strong>Date:</strong> ${formattedDate}</p>
                        <p><strong>Time:</strong> ${booking.startTime} - ${booking.endTime}</p>
                        <p><strong>Pitch Configuration:</strong> ${booking.pitchConfiguration.replace('_', ' ').toUpperCase()}</p>
                        <p><strong>Total Amount:</strong> €${booking.totalAmount.toFixed(2)}</p>
                        ${booking.notes ? `<p><strong>Notes:</strong> ${booking.notes}</p>` : ''}
                    </div>
                    
                    <p>Please arrive 15 minutes before your scheduled time. If you need to make any changes or cancel your booking, please contact us as soon as possible.</p>
                    
                    <p>We look forward to seeing you at Sirens FC!</p>
                </div>
                
                <div class="footer">
                    <p>Sirens FC | St. Paul's Bay, Malta</p>
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate admin notification email HTML
    generateAdminNotificationEmail(booking) {
        const formattedDate = new Date(booking.date).toLocaleDateString('en-GB', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .booking-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
                .customer-details { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>New Booking Received</h1>
                    <p>Sirens FC Booking System</p>
                </div>
                
                <div class="content">
                    <p>A new booking has been made through the online system.</p>
                    
                    <div class="booking-details">
                        <h3>Booking Information</h3>
                        <p><strong>Reference:</strong> ${booking.bookingReference}</p>
                        <p><strong>Facility:</strong> ${booking.facilityName}</p>
                        <p><strong>Date:</strong> ${formattedDate}</p>
                        <p><strong>Time:</strong> ${booking.startTime} - ${booking.endTime}</p>
                        <p><strong>Pitch:</strong> ${booking.pitchConfiguration.replace('_', ' ').toUpperCase()}</p>
                        <p><strong>Amount:</strong> €${booking.totalAmount.toFixed(2)}</p>
                        ${booking.notes ? `<p><strong>Notes:</strong> ${booking.notes}</p>` : ''}
                    </div>
                    
                    <div class="customer-details">
                        <h3>Customer Information</h3>
                        <p><strong>Name:</strong> ${booking.userFirstName} ${booking.userLastName}</p>
                        <p><strong>Email:</strong> ${booking.userEmail}</p>
                        <p><strong>Phone:</strong> ${booking.userPhone}</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate booking cancellation email HTML
    generateBookingCancellationEmail(booking) {
        const formattedDate = new Date(booking.date).toLocaleDateString('en-GB', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .booking-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Booking Cancelled</h1>
                    <p>Sirens FC - St. Paul's Bay, Malta</p>
                </div>
                
                <div class="content">
                    <p>Dear ${booking.userFirstName} ${booking.userLastName},</p>
                    
                    <p>Your booking has been cancelled as requested.</p>
                    
                    <div class="booking-details">
                        <h3>Cancelled Booking Details</h3>
                        <p><strong>Booking Reference:</strong> ${booking.bookingReference}</p>
                        <p><strong>Facility:</strong> ${booking.facilityName}</p>
                        <p><strong>Date:</strong> ${formattedDate}</p>
                        <p><strong>Time:</strong> ${booking.startTime} - ${booking.endTime}</p>
                        <p><strong>Amount:</strong> €${booking.totalAmount.toFixed(2)}</p>
                    </div>
                    
                    <p>We hope to see you again soon at Sirens FC!</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate admin cancellation notification
    generateAdminCancellationEmail(booking) {
        const formattedDate = new Date(booking.date).toLocaleDateString('en-GB', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #ffc107; color: #212529; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .booking-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Booking Cancelled</h1>
                    <p>Sirens FC Booking System</p>
                </div>
                
                <div class="content">
                    <p>A booking has been cancelled.</p>
                    
                    <div class="booking-details">
                        <h3>Cancelled Booking</h3>
                        <p><strong>Reference:</strong> ${booking.bookingReference}</p>
                        <p><strong>Customer:</strong> ${booking.userFirstName} ${booking.userLastName}</p>
                        <p><strong>Date:</strong> ${formattedDate}</p>
                        <p><strong>Time:</strong> ${booking.startTime} - ${booking.endTime}</p>
                        <p><strong>Amount:</strong> €${booking.totalAmount.toFixed(2)}</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
