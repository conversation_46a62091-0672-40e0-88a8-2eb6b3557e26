<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>Login Debug Tool</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestPassword123!" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe"> Remember Me
            </label>
        </div>
        <button type="submit">Test Login</button>
        <button type="button" id="clearBtn">Clear Debug</button>
    </form>
    
    <div id="debugOutput" class="debug-output"></div>

    <script>
        const debugOutput = document.getElementById('debugOutput');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            debugOutput.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        function clearDebug() {
            debugOutput.innerHTML = '';
        }
        
        document.getElementById('clearBtn').addEventListener('click', clearDebug);
        
        // Enhanced Login Manager Debug Version
        class DebugEnhancedLoginManager {
            constructor() {
                this.apiBaseUrl = '/api';
                log('DebugEnhancedLoginManager initialized');
                log(`API Base URL: ${this.apiBaseUrl}`);
            }
            
            async handleLogin(email, password, rememberMe) {
                log(`Starting login process...`);
                log(`Email: ${email}`);
                log(`Password: ${'*'.repeat(password.length)}`);
                log(`Remember Me: ${rememberMe}`);
                
                try {
                    const requestBody = {
                        email,
                        password,
                        rememberMe
                    };
                    
                    log(`Request body: ${JSON.stringify(requestBody, null, 2)}`);
                    
                    const url = `${this.apiBaseUrl}/login`;
                    log(`Making request to: ${url}`);
                    
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify(requestBody)
                    });
                    
                    log(`Response status: ${response.status} ${response.statusText}`);
                    log(`Response headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`);
                    
                    const data = await response.json();
                    log(`Response data: ${JSON.stringify(data, null, 2)}`);
                    
                    if (!response.ok) {
                        const errorMessage = data.error?.message || data.error || 'Invalid email or password. Please try again.';
                        log(`Login failed: ${errorMessage}`, 'error');
                        throw new Error(errorMessage);
                    }
                    
                    // Store authentication data
                    if (data.token) {
                        localStorage.setItem('authToken', data.token);
                        log(`Token stored in localStorage: ${data.token.substring(0, 50)}...`, 'success');
                    } else {
                        log('No token in response!', 'error');
                    }
                    
                    if (data.user) {
                        localStorage.setItem('currentUser', JSON.stringify(data.user));
                        log(`User data stored: ${JSON.stringify(data.user)}`, 'success');
                    } else {
                        log('No user data in response!', 'error');
                    }
                    
                    log('Login successful!', 'success');
                    return { success: true, data };
                    
                } catch (error) {
                    log(`Login error: ${error.message}`, 'error');
                    log(`Error stack: ${error.stack}`, 'error');
                    return { success: false, error: error.message };
                }
            }
        }
        
        const debugLoginManager = new DebugEnhancedLoginManager();
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            log('=== LOGIN ATTEMPT ===');
            
            if (!email || !password) {
                log('Validation failed: Email and password are required', 'error');
                return;
            }
            
            const result = await debugLoginManager.handleLogin(email, password, rememberMe);
            
            if (result.success) {
                log('=== LOGIN SUCCESS ===', 'success');
            } else {
                log('=== LOGIN FAILED ===', 'error');
            }
        });
        
        // Check current auth state on load
        window.addEventListener('load', () => {
            log('=== PAGE LOADED ===');
            
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('currentUser');
            
            if (token) {
                log(`Existing token found: ${token.substring(0, 50)}...`);
            } else {
                log('No existing token found');
            }
            
            if (user) {
                log(`Existing user data: ${user}`);
            } else {
                log('No existing user data found');
            }
        });
    </script>
</body>
</html>
