const jwt = require('jsonwebtoken');
const User = require('../models/User');
const database = require('../config/database');

// JWT secret from environment or default
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// Generate JWT token
function generateToken(userId, email) {
    return jwt.sign(
        { userId, email },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
    );
}

// Verify JWT token middleware
async function verifyToken(req, res, next) {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'NO_TOKEN',
                    message: 'Access token is required'
                }
            });
        }

        // Verify token
        const decoded = jwt.verify(token, JWT_SECRET);
        
        // Check if user still exists and is active
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: 'User not found or inactive'
                }
            });
        }

        // Add user to request object
        req.user = user;
        req.token = token;
        next();

    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_TOKEN',
                    message: 'Invalid token'
                }
            });
        } else if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'TOKEN_EXPIRED',
                    message: 'Token has expired'
                }
            });
        } else {
            console.error('Token verification error:', error);
            return res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Internal server error'
                }
            });
        }
    }
}

// Require authentication middleware
function requireAuth(req, res, next) {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            error: {
                code: 'AUTHENTICATION_REQUIRED',
                message: 'Authentication required'
            }
        });
    }
    next();
}

// Session management
class SessionManager {
    // Create session
    static async createSession(userId) {
        const sessionId = require('uuid').v4();
        const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
        
        await database.run(`
            INSERT INTO sessions (id, user_id, expires_at)
            VALUES (?, ?, ?)
        `, [sessionId, userId, expiresAt.toISOString()]);
        
        return sessionId;
    }

    // Validate session
    static async validateSession(sessionId) {
        const session = await database.get(`
            SELECT s.*, u.* FROM sessions s
            JOIN users u ON s.user_id = u.id
            WHERE s.id = ? AND s.expires_at > CURRENT_TIMESTAMP AND u.is_active = 1
        `, [sessionId]);
        
        return session ? new User(session) : null;
    }

    // Delete session
    static async deleteSession(sessionId) {
        await database.run('DELETE FROM sessions WHERE id = ?', [sessionId]);
    }

    // Clean expired sessions
    static async cleanExpiredSessions() {
        const result = await database.run('DELETE FROM sessions WHERE expires_at <= CURRENT_TIMESTAMP');
        return result.changes;
    }

    // Delete all user sessions
    static async deleteUserSessions(userId) {
        await database.run('DELETE FROM sessions WHERE user_id = ?', [userId]);
    }
}

// Optional session-based authentication middleware
async function verifySession(req, res, next) {
    try {
        const sessionId = req.headers['x-session-id'] || req.cookies?.sessionId;
        
        if (!sessionId) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'NO_SESSION',
                    message: 'Session ID is required'
                }
            });
        }

        const user = await SessionManager.validateSession(sessionId);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_SESSION',
                    message: 'Invalid or expired session'
                }
            });
        }

        req.user = user;
        req.sessionId = sessionId;
        next();

    } catch (error) {
        console.error('Session verification error:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Internal server error'
            }
        });
    }
}

module.exports = {
    generateToken,
    verifyToken,
    requireAuth,
    verifySession,
    SessionManager,
    JWT_SECRET,
    JWT_EXPIRES_IN
};
