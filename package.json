{"name": "sports-booking-platform", "version": "1.0.0", "description": "A comprehensive multi-sport facility booking platform for Malta", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server/index.js", "client:dev": "live-server public --port=3000", "build": "npm run client:build", "client:build": "webpack --mode=production", "start": "node server/index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["sports", "booking", "platform", "malta", "football", "tennis", "padel"], "author": "Sports Malta", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "connect-sqlite3": "^0.9.16", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.11.2", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^7.2.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pg": "^8.11.3", "redis": "^4.6.8", "socket.io": "^4.7.2", "sqlite3": "^5.1.7", "stripe": "^13.5.0", "uuid": "^9.0.1", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "babel-jest": "^29.6.2", "concurrently": "^8.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "live-server": "^1.2.2", "nodemon": "^3.0.1", "supertest": "^6.3.4", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}