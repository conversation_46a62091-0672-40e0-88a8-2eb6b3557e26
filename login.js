// Auth Manager for handling user authentication
class AuthManager {
    constructor() {
        this.apiBaseUrl = window.location.origin + '/api';
        this.initializeEventListeners();
    }

    // Initialize event listeners
    initializeEventListeners() {
        // Login form submission
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Registration form submission
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Toggle between login and register forms
        const showRegisterLink = document.getElementById('showRegister');
        const showLoginLink = document.getElementById('showLogin');
        
        if (showRegisterLink) {
            showRegisterLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleForms('register');
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleForms('login');
            });
        }

        // Password visibility toggles
        document.querySelectorAll('.password-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => this.togglePasswordVisibility(e));
        });

        // Forgot password form submission
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', (e) => this.handleForgotPassword(e));
        }

        // Forgot password link
        const showForgotPasswordLink = document.getElementById('showForgotPassword');
        if (showForgotPasswordLink) {
            showForgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showForgotPasswordForm();
            });
        }

        // Back to login from forgot password
        const showLoginFromForgot = document.getElementById('showLoginFromForgot');
        if (showLoginFromForgot) {
            showLoginFromForgot.addEventListener('click', (e) => {
                e.preventDefault();
                this.hideForgotPasswordForm();
            });
        }
    }

    // Toggle between login and register forms
    toggleForms(formToShow) {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const loginLink = document.querySelector('.register-link');
        const registerLink = document.getElementById('loginLink');
        const loginHeader = document.querySelector('.login-header');

        if (formToShow === 'register') {
            loginForm?.classList.add('hidden');
            registerForm?.classList.remove('hidden');
            loginLink?.classList.add('hidden');
            registerLink?.classList.remove('hidden');
            this.clearMessages();
            
            // Update header
            if (loginHeader) {
                loginHeader.querySelector('h2').textContent = 'Create Account';
                loginHeader.querySelector('p').textContent = 'Join our community';
            }
        } else {
            registerForm?.classList.add('hidden');
            loginForm?.classList.remove('hidden');
            registerLink?.classList.add('hidden');
            loginLink?.classList.remove('hidden');
            this.clearMessages();
            
            // Update header
            if (loginHeader) {
                loginHeader.querySelector('h2').textContent = 'Welcome Back';
                loginHeader.querySelector('p').textContent = 'Sign in to your account';
            }
            
            // Clear registration form
            registerForm?.reset();
        }
    }

    // Clear all form messages
    clearMessages() {
        const messageContainer = document.getElementById('messageContainer');
        if (messageContainer) {
            messageContainer.style.display = 'none';
            messageContainer.className = 'message-container';
        }
    }

    // Toggle password visibility
    togglePasswordVisibility(e) {
        const button = e.currentTarget;
        const input = button.previousElementSibling;
        const icon = button.querySelector('i');
        
        if (input && input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else if (input) {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Show message to user
    showMessage(message, type = 'info') {
        const messageContainer = document.getElementById('messageContainer');
        const messageElement = document.getElementById('message');
        
        if (messageElement && messageContainer) {
            messageElement.textContent = message;
            messageContainer.className = `message-container ${type}`;
            messageContainer.style.display = 'block';
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    messageContainer.style.display = 'none';
                }, 5000);
            }
        }
    }

    // Handle login form submission
    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('email')?.value.trim();
        const password = document.getElementById('password')?.value;
        const rememberMe = document.getElementById('rememberMe')?.checked;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        let originalBtnText = '';
        
        // Validate inputs
        if (!email || !password) {
            this.showMessage('Please enter both email and password', 'error');
            return;
        }
        
        try {
            // Show loading state
            if (submitBtn) {
                originalBtnText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
            }
            
            // Call login API
            const response = await fetch(`${this.apiBaseUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    password,
                    rememberMe
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Invalid email or password. Please try again.');
            }
            
            // Save token and user data
            if (data.token && data.user) {
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('currentUser', JSON.stringify(data.user));
                
                // Show success message
                this.showMessage('Login successful! Redirecting...', 'success');
                
                // Redirect to index.html after a short delay
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                throw new Error('Invalid response from server');
            }
        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText || 'Sign In';
            }
        }
    }

    // Handle registration form submission
    async handleRegister(e) {
        e.preventDefault();
        
        const name = document.getElementById('regName')?.value.trim();
        const email = document.getElementById('regEmail')?.value.trim();
        const password = document.getElementById('regPassword')?.value;
        const confirmPassword = document.getElementById('confirmPassword')?.value;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        let originalBtnText = '';
        
        try {
            // Basic validation
            if (!name || !email || !password || !confirmPassword) {
                throw new Error('Please fill in all required fields');
            }
            
            if (password !== confirmPassword) {
                throw new Error('Passwords do not match');
            }
            
            if (password.length < 6) {
                throw new Error('Password must be at least 6 characters long');
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                throw new Error('Please enter a valid email address');
            }
            
            // Show loading state
            if (submitBtn) {
                originalBtnText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating account...';
            }
            
            // Call register API
            const response = await fetch(`${this.apiBaseUrl}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    email,
                    password,
                    confirmPassword
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Registration failed. Please try again.');
            }
            
            // Show success message and switch to login form
            this.showMessage('Registration successful! You can now log in.', 'success');
            
            // Clear the registration form
            document.getElementById('registerForm')?.reset();
            
            // Switch to login form after a short delay and pre-fill email
            setTimeout(() => {
                this.toggleForms('login');
                // Pre-fill email field
                const emailField = document.getElementById('email');
                if (emailField) {
                    emailField.value = email;
                    // Focus password field
                    const passwordField = document.getElementById('password');
                    if (passwordField) {
                        passwordField.focus();
                    }
                }
            }, 2000);
            
        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText || 'Create Account';
            }
        }
    }

    // Show forgot password form
    showForgotPasswordForm() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        const registerLink = document.querySelector('.register-link');
        const loginLink = document.getElementById('loginLink');
        const backToLoginLink = document.getElementById('backToLoginLink');
        const loginHeader = document.querySelector('.login-header');

        // Hide all other forms
        loginForm?.classList.add('hidden');
        registerForm?.classList.add('hidden');
        registerLink?.classList.add('hidden');
        loginLink?.classList.add('hidden');

        // Show forgot password form
        forgotPasswordForm?.classList.remove('hidden');
        backToLoginLink?.classList.remove('hidden');

        // Update header
        if (loginHeader) {
            loginHeader.querySelector('h2').textContent = 'Reset Password';
            loginHeader.querySelector('p').textContent = 'Enter your email to receive a reset link';
        }

        // Clear messages and focus email field
        this.clearMessages();
        const forgotEmailField = document.getElementById('forgotEmail');
        if (forgotEmailField) {
            forgotEmailField.focus();
        }
    }

    // Hide forgot password form and return to login
    hideForgotPasswordForm() {
        const loginForm = document.getElementById('loginForm');
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        const registerLink = document.querySelector('.register-link');
        const backToLoginLink = document.getElementById('backToLoginLink');
        const loginHeader = document.querySelector('.login-header');

        // Hide forgot password form
        forgotPasswordForm?.classList.add('hidden');
        backToLoginLink?.classList.add('hidden');

        // Show login form
        loginForm?.classList.remove('hidden');
        registerLink?.classList.remove('hidden');

        // Update header back to login
        if (loginHeader) {
            loginHeader.querySelector('h2').textContent = 'Welcome Back';
            loginHeader.querySelector('p').textContent = 'Sign in to your account';
        }

        // Clear messages and reset form
        this.clearMessages();
        forgotPasswordForm?.reset();
        
        // Clear any error states
        const forgotEmailError = document.getElementById('forgotEmailError');
        if (forgotEmailError) {
            forgotEmailError.classList.add('hidden');
        }
        
        const forgotEmailField = document.getElementById('forgotEmail');
        if (forgotEmailField) {
            forgotEmailField.classList.remove('input-error');
        }
    }

    // Handle forgot password form submission
    async handleForgotPassword(e) {
        e.preventDefault();
        
        const email = document.getElementById('forgotEmail')?.value.trim();
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const forgotEmailField = document.getElementById('forgotEmail');
        const forgotEmailError = document.getElementById('forgotEmailError');
        let originalBtnText = '';
        
        // Clear previous errors
        if (forgotEmailError) {
            forgotEmailError.classList.add('hidden');
        }
        if (forgotEmailField) {
            forgotEmailField.classList.remove('input-error');
        }
        
        // Validate email
        if (!email) {
            this.showForgotPasswordError('Please enter your email address');
            return;
        }
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.showForgotPasswordError('Please enter a valid email address');
            return;
        }
        
        try {
            // Show loading state
            if (submitBtn) {
                originalBtnText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            }
            
            // Call forgot password API
            const response = await fetch(`${this.apiBaseUrl}/forgot-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to send reset email. Please try again.');
            }
            
            // Show success message
            this.showMessage('If an account with that email exists, a password reset link has been sent. Please check your email.', 'success');
            
            // Clear the form
            document.getElementById('forgotPasswordForm')?.reset();
            
        } catch (error) {
            this.showForgotPasswordError(error.message);
        } finally {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText || '<span class="btn-text">Send Reset Link</span><i class="fas fa-paper-plane btn-icon"></i>';
            }
        }
    }

    // Show forgot password specific error
    showForgotPasswordError(message) {
        const forgotEmailField = document.getElementById('forgotEmail');
        const forgotEmailError = document.getElementById('forgotEmailError');
        
        if (forgotEmailField) {
            forgotEmailField.classList.add('input-error');
        }
        
        if (forgotEmailError) {
            forgotEmailError.querySelector('span').textContent = message;
            forgotEmailError.classList.remove('hidden');
        }
    }
}

// Initialize the auth manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is already logged in
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');
    
    if (authToken && currentUser) {
        // User is already logged in, redirect to home
        window.location.href = 'index.html';
        return;
    }
    
    // Initialize AuthManager if not already logged in
    window.authManager = new AuthManager();
    
    // Show login form by default
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('register') === 'true') {
        window.authManager.toggleForms('register');
    }
    
    // Auto-focus the email field on login form
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.focus();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
