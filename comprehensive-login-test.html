<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔍 Comprehensive Login System Test</h1>
    
    <!-- Server Status Check -->
    <div class="test-section">
        <h2>1. Server Status Check</h2>
        <button onclick="checkServerStatus()">Check Server Status</button>
        <div id="serverStatus"></div>
    </div>

    <!-- API Endpoint Tests -->
    <div class="test-section">
        <h2>2. API Endpoint Tests</h2>
        <button onclick="testRegisterEndpoint()">Test /api/register</button>
        <button onclick="testLoginEndpoint()">Test /api/login</button>
        <button onclick="testVerifyEndpoint()">Test /api/verify</button>
        <div id="apiResults"></div>
    </div>

    <!-- User Registration Test -->
    <div class="test-section">
        <h2>3. User Registration Test</h2>
        <form id="regForm">
            <div class="form-group">
                <label>Name:</label>
                <input type="text" id="regName" value="Test User Registration">
            </div>
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="regEmail" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="regPassword" value="TestPassword123!">
            </div>
            <div class="form-group">
                <label>Confirm Password:</label>
                <input type="password" id="regConfirmPassword" value="TestPassword123!">
            </div>
            <button type="submit">Register User</button>
        </form>
        <div id="regResults"></div>
    </div>

    <!-- User Login Test -->
    <div class="test-section">
        <h2>4. User Login Test</h2>
        <form id="loginForm">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="loginEmail" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="loginPassword" value="TestPassword123!">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="rememberMe"> Remember Me
                </label>
            </div>
            <button type="submit">Login User</button>
        </form>
        <div id="loginResults"></div>
    </div>

    <!-- Enhanced Login Manager Test -->
    <div class="test-section">
        <h2>5. Enhanced Login Manager Test</h2>
        <button onclick="testEnhancedLogin()">Test Enhanced Login Manager</button>
        <div id="enhancedResults"></div>
    </div>

    <!-- Current Auth State -->
    <div class="test-section">
        <h2>6. Current Authentication State</h2>
        <button onclick="checkAuthState()">Check Auth State</button>
        <button onclick="clearAuthState()">Clear Auth State</button>
        <div id="authState"></div>
    </div>

    <!-- Debug Console -->
    <div class="test-section">
        <h2>7. Debug Console</h2>
        <button onclick="clearDebug()">Clear Debug</button>
        <div id="debugConsole" class="debug"></div>
    </div>

    <script>
        const API_BASE = '/api';
        const debugConsole = document.getElementById('debugConsole');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            debugConsole.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        function clearDebug() {
            debugConsole.textContent = '';
        }
        
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = isSuccess ? 'success' : 'error';
            element.innerHTML = message;
        }
        
        // 1. Server Status Check
        async function checkServerStatus() {
            log('Checking server status...');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('serverStatus', `✅ Server is online<br>Status: ${data.status}<br>Time: ${data.timestamp}`, true);
                    log('Server status check: SUCCESS');
                } else {
                    showResult('serverStatus', `❌ Server responded with error: ${response.status}`, false);
                    log(`Server status check: ERROR ${response.status}`);
                }
            } catch (error) {
                showResult('serverStatus', `❌ Server is offline or unreachable<br>Error: ${error.message}`, false);
                log(`Server status check: FAILED - ${error.message}`, 'error');
            }
        }
        
        // 2. API Endpoint Tests
        async function testRegisterEndpoint() {
            log('Testing /api/register endpoint...');
            try {
                const testData = {
                    name: 'API Test User',
                    email: '<EMAIL>',
                    password: 'TestPassword123!',
                    confirmPassword: 'TestPassword123!'
                };
                
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('apiResults', `✅ Register endpoint working<br>Response: ${JSON.stringify(data, null, 2)}`, true);
                    log('Register endpoint test: SUCCESS');
                } else {
                    showResult('apiResults', `⚠️ Register endpoint responded with error<br>Status: ${response.status}<br>Error: ${JSON.stringify(data, null, 2)}`, false);
                    log(`Register endpoint test: ERROR ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult('apiResults', `❌ Register endpoint failed<br>Error: ${error.message}`, false);
                log(`Register endpoint test: FAILED - ${error.message}`, 'error');
            }
        }
        
        async function testLoginEndpoint() {
            log('Testing /api/login endpoint...');
            try {
                const testData = {
                    email: '<EMAIL>',
                    password: 'TestPassword123!',
                    rememberMe: false
                };
                
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('apiResults', `✅ Login endpoint working<br>Token: ${data.token ? data.token.substring(0, 50) + '...' : 'No token'}<br>User: ${JSON.stringify(data.user)}`, true);
                    log('Login endpoint test: SUCCESS');
                    
                    // Store token for verify test
                    if (data.token) {
                        localStorage.setItem('testToken', data.token);
                    }
                } else {
                    showResult('apiResults', `⚠️ Login endpoint responded with error<br>Status: ${response.status}<br>Error: ${JSON.stringify(data, null, 2)}`, false);
                    log(`Login endpoint test: ERROR ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult('apiResults', `❌ Login endpoint failed<br>Error: ${error.message}`, false);
                log(`Login endpoint test: FAILED - ${error.message}`, 'error');
            }
        }
        
        async function testVerifyEndpoint() {
            log('Testing /api/verify endpoint...');
            const token = localStorage.getItem('testToken') || localStorage.getItem('authToken');
            
            if (!token) {
                showResult('apiResults', `⚠️ No token available for verify test. Please login first.`, false);
                log('Verify endpoint test: SKIPPED - No token available');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/verify`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('apiResults', `✅ Verify endpoint working<br>User: ${JSON.stringify(data.user)}`, true);
                    log('Verify endpoint test: SUCCESS');
                } else {
                    showResult('apiResults', `⚠️ Verify endpoint responded with error<br>Status: ${response.status}<br>Error: ${JSON.stringify(data, null, 2)}`, false);
                    log(`Verify endpoint test: ERROR ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult('apiResults', `❌ Verify endpoint failed<br>Error: ${error.message}`, false);
                log(`Verify endpoint test: FAILED - ${error.message}`, 'error');
            }
        }
        
        // 3. Registration Form Test
        document.getElementById('regForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            log('Testing registration form...');
            
            const formData = {
                name: document.getElementById('regName').value,
                email: document.getElementById('regEmail').value,
                password: document.getElementById('regPassword').value,
                confirmPassword: document.getElementById('regConfirmPassword').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('regResults', `✅ Registration successful!<br>User ID: ${data.userId}<br>Message: ${data.message}`, true);
                    log('Registration form test: SUCCESS');
                } else {
                    showResult('regResults', `❌ Registration failed<br>Error: ${data.error}`, false);
                    log(`Registration form test: FAILED - ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('regResults', `❌ Registration error<br>Error: ${error.message}`, false);
                log(`Registration form test: ERROR - ${error.message}`, 'error');
            }
        });
        
        // 4. Login Form Test
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            log('Testing login form...');
            
            const formData = {
                email: document.getElementById('loginEmail').value,
                password: document.getElementById('loginPassword').value,
                rememberMe: document.getElementById('rememberMe').checked
            };
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('currentUser', JSON.stringify(data.user));
                    
                    showResult('loginResults', `✅ Login successful!<br>Token: ${data.token.substring(0, 50)}...<br>User: ${JSON.stringify(data.user)}`, true);
                    log('Login form test: SUCCESS');
                } else {
                    showResult('loginResults', `❌ Login failed<br>Error: ${data.error}`, false);
                    log(`Login form test: FAILED - ${data.error}`, 'error');
                }
            } catch (error) {
                showResult('loginResults', `❌ Login error<br>Error: ${error.message}`, false);
                log(`Login form test: ERROR - ${error.message}`, 'error');
            }
        });
        
        // 5. Enhanced Login Manager Test
        async function testEnhancedLogin() {
            log('Testing Enhanced Login Manager...');
            
            // Check if enhanced-login.js is loaded
            if (typeof EnhancedLoginManager === 'undefined') {
                showResult('enhancedResults', `❌ EnhancedLoginManager class not found<br>The enhanced-login.js file may not be loaded properly.`, false);
                log('Enhanced Login Manager test: FAILED - Class not found', 'error');
                return;
            }
            
            try {
                const manager = new EnhancedLoginManager();
                showResult('enhancedResults', `✅ EnhancedLoginManager created successfully<br>API Base URL: ${manager.apiBaseUrl}`, true);
                log('Enhanced Login Manager test: SUCCESS');
            } catch (error) {
                showResult('enhancedResults', `❌ Failed to create EnhancedLoginManager<br>Error: ${error.message}`, false);
                log(`Enhanced Login Manager test: FAILED - ${error.message}`, 'error');
            }
        }
        
        // 6. Auth State Management
        function checkAuthState() {
            log('Checking current authentication state...');
            
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('currentUser');
            
            let stateHtml = '<h4>LocalStorage Contents:</h4>';
            stateHtml += `<strong>authToken:</strong> ${token ? token.substring(0, 50) + '...' : 'Not found'}<br>`;
            stateHtml += `<strong>currentUser:</strong> ${user || 'Not found'}<br>`;
            
            if (token && user) {
                stateHtml += '<br><div class="success">✅ User appears to be logged in</div>';
                log('Auth state check: User is logged in');
            } else {
                stateHtml += '<br><div class="error">❌ User is not logged in</div>';
                log('Auth state check: User is not logged in');
            }
            
            document.getElementById('authState').innerHTML = stateHtml;
        }
        
        function clearAuthState() {
            log('Clearing authentication state...');
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('testToken');
            showResult('authState', '✅ Authentication state cleared', true);
            log('Auth state cleared');
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('=== Comprehensive Login Test Initialized ===');
            checkServerStatus();
            checkAuthState();
        });
    </script>
</body>
</html>
