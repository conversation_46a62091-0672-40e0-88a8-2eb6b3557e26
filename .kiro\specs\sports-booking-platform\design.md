cr# Sports Booking Platform - Design Document

## Overview

The Sports Booking Platform is designed as a modern, responsive web application that provides a centralized booking system for multiple sports facilities across Malta. The system follows a modular architecture with clear separation of concerns, real-time data synchronization, and mobile-first responsive design principles.

### Key Design Principles
- **User-Centric Design**: Intuitive navigation and booking flow
- **Real-Time Synchronization**: Live availability updates across all users
- **Scalable Architecture**: Support for multiple venues and sports types
- **Mobile-First Approach**: Responsive design optimized for all devices
- **Performance Optimization**: Fast loading times and smooth interactions

## Architecture

### System Architecture
The platform follows a client-server architecture with the following layers:

```
┌─────────────────────────────────────────┐
│           Frontend (Client)             │
│  ┌─────────────┐ ┌─────────────────────┐ │
│  │   Web App   │ │   Mobile Web App    │ │
│  │  (Desktop)  │ │    (Responsive)     │ │
│  └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────┘
                    │
                    │ HTTPS/REST API
                    │
┌─────────────────────────────────────────┐
│            Backend Services             │
│  ┌─────────────┐ ┌─────────────────────┐ │
│  │   API       │ │   Real-time         │ │
│  │  Gateway    │ │   WebSocket         │ │
│  └─────────────┘ └─────────────────────┘ │
│  ┌─────────────┐ ┌─────────────────────┐ │
│  │  Booking    │ │   Payment           │ │
│  │  Service    │ │   Service           │ │
│  └─────────────┘ └─────────────────────┘ │
│  ┌─────────────┐ ┌─────────────────────┐ │
│  │   User      │ │   Notification      │ │
│  │  Service    │ │   Service           │ │
│  └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│            Data Layer                   │
│  ┌─────────────┐ ┌─────────────────────┐ │
│  │  Primary    │ │   Cache Layer       │ │
│  │  Database   │ │   (Redis)           │ │
│  │ (PostgreSQL)│ └─────────────────────┘ │
│  └─────────────┘                       │
└─────────────────────────────────────────┘
```

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), responsive framework
- **Backend**: Node.js with Express.js or similar framework
- **Database**: PostgreSQL for primary data, Redis for caching and sessions
- **Real-time**: WebSocket for live availability updates
- **Payment**: Integration with payment gateway (Stripe/PayPal)
- **Email**: SMTP service for notifications and confirmations

## Components and Interfaces

### Frontend Components

#### 1. Navigation Component
- **Sticky Header**: Remains visible during scroll
- **Responsive Menu**: Hamburger menu for mobile devices
- **Logo and Branding**: SM logo with consistent styling
- **User Authentication**: Login/logout functionality

#### 2. Sport Selection Component
- **Sport Cards**: Interactive cards for Football, Tennis, Padel
- **Visual Design**: High-quality images with overlay text
- **Hover Effects**: Smooth transitions and visual feedback
- **Responsive Grid**: Adapts to different screen sizes

#### 3. Venue Selection Component
- **Venue Cards**: Display club information and facilities
- **Location Information**: Address and basic details
- **Availability Indicator**: Real-time status updates
- **Filtering Options**: Search and filter by location/features

#### 4. Calendar Component
- **Interactive Calendar**: Month view with navigation
- **Date Selection**: Clickable dates with visual feedback
- **Availability Display**: Color-coded available/unavailable dates
- **Mobile Optimization**: Touch-friendly interface

#### 5. Time Slot Component
- **Time Grid**: Available slots for selected date
- **Real-time Updates**: Live availability synchronization
- **Pricing Display**: Clear pricing per time slot
- **Selection Interface**: Single-click slot selection

#### 6. Booking Form Component
- **User Information**: Contact details and preferences
- **Booking Summary**: Selected details and total cost
- **Payment Integration**: Secure payment processing
- **Validation**: Real-time form validation

#### 7. User Dashboard Component
- **Booking History**: Past and upcoming reservations
- **Profile Management**: User information updates
- **Quick Actions**: Cancel, modify, or rebook options
- **Notifications**: System messages and reminders

### Backend Services

#### 1. API Gateway
- **Route Management**: Centralized API routing
- **Authentication**: JWT token validation
- **Rate Limiting**: API usage protection
- **CORS Handling**: Cross-origin request management

#### 2. Booking Service
- **Availability Management**: Real-time slot tracking
- **Reservation Logic**: Booking creation and validation
- **Conflict Resolution**: Double-booking prevention
- **Business Rules**: Pricing and policy enforcement

#### 3. User Service
- **Authentication**: Login/registration handling
- **Profile Management**: User data operations
- **Session Management**: Secure session handling
- **Password Security**: Hashing and validation

#### 4. Payment Service
- **Payment Processing**: Secure transaction handling
- **Refund Management**: Cancellation and refund logic
- **Invoice Generation**: Receipt and confirmation emails
- **Fraud Protection**: Security measures and validation

#### 5. Notification Service
- **Email Templates**: Booking confirmations and reminders
- **SMS Integration**: Optional text notifications
- **Newsletter Management**: Subscription handling
- **Real-time Alerts**: WebSocket notifications

## Data Models

### Core Entities

#### User Model
```javascript
{
  id: UUID,
  email: String (unique),
  password: String (hashed),
  firstName: String,
  lastName: String,
  phone: String,
  dateCreated: DateTime,
  lastLogin: DateTime,
  isActive: Boolean,
  preferences: {
    notifications: Boolean,
    newsletter: Boolean,
    preferredSports: Array
  }
}
```

#### Venue Model
```javascript
{
  id: UUID,
  name: String,
  description: Text,
  address: {
    street: String,
    city: String,
    postalCode: String,
    coordinates: {lat: Number, lng: Number}
  },
  facilities: Array,
  amenities: Array,
  images: Array,
  contactInfo: {
    phone: String,
    email: String,
    website: String
  },
  isActive: Boolean
}
```

#### Facility Model
```javascript
{
  id: UUID,
  venueId: UUID,
  name: String,
  sportType: Enum ['football', 'tennis', 'padel'],
  facilityType: String, // '5-a-side', 'full-pitch', 'clay-court'
  capacity: Number,
  features: Array, // ['floodlit', 'changing-rooms', 'parking']
  surfaceType: String,
  hourlyRate: {
    peak: Number,
    offPeak: Number,
    weekend: Number
  },
  operatingHours: {
    monday: {open: Time, close: Time},
    tuesday: {open: Time, close: Time},
    // ... other days
  },
  isActive: Boolean
}
```

#### Booking Model
```javascript
{
  id: UUID,
  userId: UUID,
  facilityId: UUID,
  bookingDate: Date,
  startTime: Time,
  endTime: Time,
  duration: Number, // in hours
  totalCost: Number,
  status: Enum ['pending', 'confirmed', 'cancelled', 'completed'],
  paymentStatus: Enum ['pending', 'paid', 'refunded'],
  paymentId: String,
  bookingReference: String,
  specialRequests: Text,
  createdAt: DateTime,
  updatedAt: DateTime,
  cancelledAt: DateTime,
  cancellationReason: String
}
```

#### Availability Model
```javascript
{
  id: UUID,
  facilityId: UUID,
  date: Date,
  timeSlots: [
    {
      startTime: Time,
      endTime: Time,
      isAvailable: Boolean,
      bookingId: UUID, // if booked
      price: Number
    }
  ],
  lastUpdated: DateTime
}
```

### Relationships
- **User** → **Booking** (One-to-Many)
- **Venue** → **Facility** (One-to-Many)
- **Facility** → **Booking** (One-to-Many)
- **Facility** → **Availability** (One-to-Many)

## Error Handling

### Frontend Error Handling
- **Network Errors**: Retry mechanisms with user feedback
- **Validation Errors**: Real-time field validation with clear messages
- **Session Expiry**: Automatic redirect to login with state preservation
- **Payment Failures**: Clear error messages with retry options

### Backend Error Handling
- **Database Errors**: Graceful degradation with appropriate HTTP status codes
- **Payment Errors**: Detailed error logging with user-friendly messages
- **Booking Conflicts**: Real-time conflict detection and resolution
- **Rate Limiting**: Clear messaging when limits are exceeded

### Error Response Format
```javascript
{
  success: false,
  error: {
    code: 'BOOKING_CONFLICT',
    message: 'This time slot is no longer available',
    details: {
      conflictingBooking: bookingId,
      suggestedAlternatives: [timeSlots]
    }
  }
}
```

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest and React Testing Library
- **Integration Tests**: User flow testing with Cypress
- **Visual Regression**: Screenshot comparison testing
- **Accessibility Testing**: WCAG compliance validation
- **Performance Testing**: Lighthouse audits and Core Web Vitals

### Backend Testing
- **Unit Tests**: Service and utility function testing
- **Integration Tests**: API endpoint testing with supertest
- **Database Tests**: Repository pattern testing with test database
- **Load Testing**: Concurrent booking scenario testing
- **Security Testing**: Authentication and authorization validation

### End-to-End Testing
- **Booking Flow**: Complete user journey from selection to confirmation
- **Payment Processing**: Full payment cycle including refunds
- **Real-time Updates**: Multi-user availability synchronization
- **Mobile Responsiveness**: Cross-device functionality testing
- **Browser Compatibility**: Cross-browser testing matrix

### Test Data Management
- **Seed Data**: Consistent test venue and facility data
- **Mock Services**: Payment gateway and email service mocking
- **Test Users**: Predefined user accounts for different scenarios
- **Cleanup Procedures**: Automated test data cleanup after runs

## Performance Considerations

### Frontend Optimization
- **Code Splitting**: Lazy loading of route components
- **Image Optimization**: WebP format with fallbacks
- **Caching Strategy**: Service worker for offline functionality
- **Bundle Optimization**: Tree shaking and minification

### Backend Optimization
- **Database Indexing**: Optimized queries for availability checks
- **Caching Layer**: Redis for frequently accessed data
- **Connection Pooling**: Efficient database connection management
- **API Response Compression**: Gzip compression for API responses

### Real-time Performance
- **WebSocket Optimization**: Efficient message broadcasting
- **Debouncing**: Prevent excessive availability updates
- **Connection Management**: Graceful WebSocket reconnection
- **Scalability**: Horizontal scaling with load balancers