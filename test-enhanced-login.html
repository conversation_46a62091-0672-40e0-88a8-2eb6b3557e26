<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Enhanced Login Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestPassword123!" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe"> Remember Me
            </label>
        </div>
        <button type="submit" id="loginButton">
            <span class="btn-text">Sign In</span>
            <span class="btn-loading" style="display: none;">
                <span class="loading-spinner"></span>
                Signing in...
            </span>
        </button>
    </form>
    
    <div id="result"></div>
    <div id="debug" class="debug"></div>

    <script>
        // Copy the EnhancedLoginManager class from enhanced-login.js
        class EnhancedLoginManager {
            constructor() {
                this.apiBaseUrl = '/api';
                this.csrfToken = null;
                this.currentForm = 'login';
                
                this.debug('EnhancedLoginManager initialized');
                this.debug(`API Base URL: ${this.apiBaseUrl}`);
            }

            debug(message) {
                const debugDiv = document.getElementById('debug');
                const timestamp = new Date().toLocaleTimeString();
                debugDiv.textContent += `[${timestamp}] ${message}\n`;
            }

            async handleLogin(e) {
                e.preventDefault();
                
                const email = document.getElementById('email')?.value.trim();
                const password = document.getElementById('password')?.value;
                const rememberMe = document.getElementById('rememberMe')?.checked;
                const submitBtn = document.getElementById('loginButton');
                
                this.debug(`Login attempt: ${email}, remember: ${rememberMe}`);
                
                // Validate inputs
                if (!email || !password) {
                    this.showMessage('Please enter both email and password', 'error');
                    return;
                }
                
                try {
                    // Show loading state
                    this.setButtonLoading(submitBtn, true, 'Signing in...');
                    
                    this.debug('Making API request...');
                    
                    // Call login API
                    const response = await fetch(`${this.apiBaseUrl}/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            email,
                            password,
                            rememberMe
                        })
                    });
                    
                    this.debug(`Response status: ${response.status}`);
                    
                    const data = await response.json();
                    this.debug(`Response data: ${JSON.stringify(data, null, 2)}`);
                    
                    if (!response.ok) {
                        throw new Error(data.error?.message || data.error || 'Invalid email or password. Please try again.');
                    }
                    
                    // Store authentication data
                    if (data.token) {
                        localStorage.setItem('authToken', data.token);
                        this.debug('Token stored in localStorage');
                    }
                    if (data.user) {
                        localStorage.setItem('currentUser', JSON.stringify(data.user));
                        this.debug('User data stored in localStorage');
                    }

                    this.showMessage('Login successful!', 'success');
                    this.debug('Login completed successfully');

                } catch (error) {
                    this.debug(`Login error: ${error.message}`);
                    this.showMessage(error.message, 'error');
                } finally {
                    this.setButtonLoading(submitBtn, false);
                }
            }

            setButtonLoading(button, isLoading, loadingText = 'Loading...') {
                if (!button) return;

                const btnText = button.querySelector('.btn-text');
                const btnLoading = button.querySelector('.btn-loading');

                if (isLoading) {
                    button.disabled = true;
                    if (btnText) btnText.style.display = 'none';
                    if (btnLoading) {
                        btnLoading.style.display = 'inline-flex';
                        if (loadingText) {
                            const loadingTextSpan = btnLoading.querySelector('span:last-child');
                            if (loadingTextSpan) loadingTextSpan.textContent = loadingText;
                        }
                    }
                } else {
                    button.disabled = false;
                    if (btnText) btnText.style.display = 'inline';
                    if (btnLoading) btnLoading.style.display = 'none';
                }
            }

            showMessage(message, type) {
                const result = document.getElementById('result');
                result.className = `result ${type}`;
                result.textContent = message;
            }
        }

        // Initialize the enhanced login manager
        const enhancedLoginManager = new EnhancedLoginManager();
        
        // Attach event listener
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            enhancedLoginManager.handleLogin(e);
        });
        
        // Check current auth state on load
        window.addEventListener('load', () => {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('currentUser');
            
            if (token && user) {
                enhancedLoginManager.showMessage(`Already logged in as: ${JSON.parse(user).name}`, 'success');
                enhancedLoginManager.debug(`Existing auth found - Token: ${token.substring(0, 50)}...`);
            } else {
                enhancedLoginManager.debug('No existing authentication found');
            }
        });
    </script>
</body>
</html>
