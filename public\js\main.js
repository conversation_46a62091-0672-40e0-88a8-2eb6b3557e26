// Sports Booking Platform - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    }

    // Handle account link authentication check
    const accountLinks = document.querySelectorAll('a[href="account.html"], a[href="/account.html"]');
    accountLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Check if user is authenticated
            if (window.authHandler && window.authHandler.isAuthenticated()) {
                // Authenticated, go to account page
                window.location.href = '/account.html';
            } else {
                // Not authenticated, redirect to login
                window.location.href = '/login.html?returnUrl=' + encodeURIComponent('/account.html');
            }
        });
    });

    // Update navigation based on auth state
    updateNavigationAuthState();

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Newsletter form submission
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Basic email validation
            if (validateEmail(email)) {
                // Simulate API call
                showNotification('Thank you for subscribing! You will receive updates soon.', 'success');
                this.reset();
            } else {
                showNotification('Please enter a valid email address.', 'error');
            }
        });
    }

    // Sport card interactions
    const sportCards = document.querySelectorAll('.sport-card');
    sportCards.forEach(card => {
        card.addEventListener('click', function() {
            const sport = this.dataset.sport;
            const link = this.querySelector('.btn-sport');
            if (link) {
                window.location.href = link.href;
            }
        });

        // Add hover effect for better UX
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.sport-card, .feature-card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Add loading states for images
    const images = document.querySelectorAll('img[loading="lazy"]');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
        
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
    });
});

// Utility Functions
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px',
        wordWrap: 'break-word'
    });

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#00dc30';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        default:
            notification.style.backgroundColor = '#007bff';
    }

    // Add to DOM
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);

    // Allow manual dismissal
    notification.addEventListener('click', function() {
        this.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (this.parentNode) {
                this.parentNode.removeChild(this);
            }
        }, 300);
    });
}

// API Helper Functions (for future use)
const API = {
    baseURL: '/api',
    
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Something went wrong');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    },

    // Newsletter subscription
    async subscribeNewsletter(email) {
        return this.request('/newsletter/subscribe', {
            method: 'POST',
            body: JSON.stringify({ email })
        });
    },

    // Get sports data
    async getSports() {
        return this.request('/sports');
    },

    // Get venues
    async getVenues(sportType = null) {
        const query = sportType ? `?sport=${sportType}` : '';
        return this.request(`/venues${query}`);
    }
};

// Update navigation based on authentication state
function updateNavigationAuthState() {
    // Check if auth handler is available and user is authenticated
    const isAuthenticated = window.authHandler && window.authHandler.isAuthenticated();
    const currentUser = isAuthenticated ? window.authHandler.getCurrentUser() : null;

    // Find account links and update their behavior
    const accountLinks = document.querySelectorAll('a[href="account.html"], a[href="/account.html"]');

    if (isAuthenticated && currentUser) {
        // User is logged in - account links should work normally
        accountLinks.forEach(link => {
            link.style.opacity = '1';
            link.title = `Go to your account (${currentUser.firstName} ${currentUser.lastName})`;
            link.textContent = 'Account';
        });
    } else {
        // User is not logged in - account links should indicate login needed
        accountLinks.forEach(link => {
            link.style.opacity = '0.7';
            link.title = 'Login to access your account';
            link.textContent = 'Login';
        });
    }

    // Update any logout buttons
    const logoutButtons = document.querySelectorAll('.logout-btn, [data-action="logout"]');
    logoutButtons.forEach(button => {
        button.style.display = isAuthenticated ? 'block' : 'none';
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            if (window.authHandler) {
                await window.authHandler.logout();
                window.location.href = '/index.html';
            }
        });
    });
}

// Listen for storage changes to update nav state
window.addEventListener('storage', function(e) {
    if (e.key === 'authToken') {
        updateNavigationAuthState();
    }
});

// Listen for auth state changes
document.addEventListener('authStateChanged', function() {
    updateNavigationAuthState();
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API, validateEmail, showNotification, updateNavigationAuthState };
}