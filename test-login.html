<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Login System Test</h1>
    
    <h2>Test User Registration</h2>
    <form id="registerForm">
        <div class="form-group">
            <label for="regName">Name:</label>
            <input type="text" id="regName" value="Test User 2" required>
        </div>
        <div class="form-group">
            <label for="regEmail">Email:</label>
            <input type="email" id="regEmail" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="regPassword">Password:</label>
            <input type="password" id="regPassword" value="TestPassword123!" required>
        </div>
        <div class="form-group">
            <label for="regConfirmPassword">Confirm Password:</label>
            <input type="password" id="regConfirmPassword" value="TestPassword123!" required>
        </div>
        <button type="submit">Register</button>
    </form>
    
    <h2>Test User Login</h2>
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestPassword123!" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe"> Remember Me
            </label>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <h2>Test Token Verification</h2>
    <button id="verifyBtn">Verify Current Token</button>
    
    <h2>Test Logout</h2>
    <button id="logoutBtn">Logout</button>
    
    <div id="result"></div>

    <script>
        const API_BASE = '/api';
        
        function showResult(message, isSuccess = true) {
            const result = document.getElementById('result');
            result.className = `result ${isSuccess ? 'success' : 'error'}`;
            result.innerHTML = message;
        }
        
        // Register form
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('regName').value,
                email: document.getElementById('regEmail').value,
                password: document.getElementById('regPassword').value,
                confirmPassword: document.getElementById('regConfirmPassword').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Registration successful! User ID: ${data.userId}`, true);
                } else {
                    showResult(`Registration failed: ${data.error}`, false);
                }
            } catch (error) {
                showResult(`Registration error: ${error.message}`, false);
            }
        });
        
        // Login form
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                rememberMe: document.getElementById('rememberMe').checked
            };
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('currentUser', JSON.stringify(data.user));
                    showResult(`Login successful! Token: ${data.token.substring(0, 50)}...<br>User: ${JSON.stringify(data.user)}`, true);
                } else {
                    showResult(`Login failed: ${data.error}`, false);
                }
            } catch (error) {
                showResult(`Login error: ${error.message}`, false);
            }
        });
        
        // Verify token
        document.getElementById('verifyBtn').addEventListener('click', async () => {
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                showResult('No token found in localStorage', false);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/verify`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Token verification successful!<br>User: ${JSON.stringify(data.user)}`, true);
                } else {
                    showResult(`Token verification failed: ${data.error}`, false);
                }
            } catch (error) {
                showResult(`Token verification error: ${error.message}`, false);
            }
        });
        
        // Logout
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                showResult('No token found in localStorage', false);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('currentUser');
                    showResult('Logout successful!', true);
                } else {
                    showResult(`Logout failed: ${data.error}`, false);
                }
            } catch (error) {
                showResult(`Logout error: ${error.message}`, false);
            }
        });
        
        // Check current auth state on load
        window.addEventListener('load', () => {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('currentUser');
            
            if (token && user) {
                showResult(`Already logged in!<br>Token: ${token.substring(0, 50)}...<br>User: ${user}`, true);
            } else {
                showResult('Not logged in', false);
            }
        });
    </script>
</body>
</html>
