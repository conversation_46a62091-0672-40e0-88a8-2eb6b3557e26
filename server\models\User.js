const database = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
    constructor(data) {
        this.id = data.id;
        this.firstName = data.first_name;
        this.lastName = data.last_name;
        this.email = data.email;
        this.phone = data.phone;
        this.passwordHash = data.password_hash;
        this.isActive = data.is_active;
        this.createdAt = data.created_at;
        this.updatedAt = data.updated_at;
    }

    // Create a new user
    static async create(userData) {
        const { firstName, lastName, email, phone, password } = userData;
        
        // Hash password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        
        const result = await database.run(`
            INSERT INTO users (first_name, last_name, email, phone, password_hash)
            VALUES (?, ?, ?, ?, ?)
        `, [firstName, lastName, email, phone, passwordHash]);
        
        // Fetch the created user
        const user = await database.get('SELECT * FROM users WHERE id = ?', [result.id]);
        return new User(user);
    }

    // Find user by email
    static async findByEmail(email) {
        const user = await database.get('SELECT * FROM users WHERE email = ? AND is_active = 1', [email]);
        return user ? new User(user) : null;
    }

    // Find user by ID
    static async findById(id) {
        const user = await database.get('SELECT * FROM users WHERE id = ? AND is_active = 1', [id]);
        return user ? new User(user) : null;
    }

    // Verify password
    async verifyPassword(password) {
        return await bcrypt.compare(password, this.passwordHash);
    }

    // Update user's last login
    async updateLastLogin() {
        await database.run(
            'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [this.id]
        );
    }

    // Get user data without sensitive information
    toJSON() {
        return {
            id: this.id,
            firstName: this.firstName,
            lastName: this.lastName,
            email: this.email,
            phone: this.phone,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    // Update user information
    async update(updateData) {
        const allowedFields = ['first_name', 'last_name', 'phone'];
        const updates = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            if (allowedFields.includes(key) && value !== undefined) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updates.length === 0) {
            return this;
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(this.id);

        await database.run(
            `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
            values
        );

        // Fetch updated user
        const updatedUser = await User.findById(this.id);
        return updatedUser;
    }

    // Change password
    async changePassword(newPassword) {
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(newPassword, saltRounds);
        
        await database.run(
            'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [passwordHash, this.id]
        );
    }

    // Deactivate user account
    async deactivate() {
        await database.run(
            'UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [this.id]
        );
        this.isActive = 0;
    }

    // Get user's bookings
    async getBookings() {
        const bookings = await database.all(`
            SELECT b.*, f.name as facility_name, f.location as facility_location
            FROM bookings b
            JOIN facilities f ON b.facility_id = f.id
            WHERE b.user_id = ?
            ORDER BY b.date DESC, b.start_time DESC
        `, [this.id]);
        
        return bookings;
    }

    // Check if email exists (static method for validation)
    static async emailExists(email, excludeUserId = null) {
        let query = 'SELECT id FROM users WHERE email = ?';
        let params = [email];

        if (excludeUserId) {
            query += ' AND id != ?';
            params.push(excludeUserId);
        }

        const user = await database.get(query, params);
        return !!user;
    }

    // Admin methods

    // Get dashboard statistics
    static async getStats() {
        try {
            const today = new Date().toISOString().split('T')[0];

            const totalUsersResult = await database.get('SELECT COUNT(*) as count FROM users');
            const newUsersTodayResult = await database.get(
                'SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = ?',
                [today]
            );
            const activeUsersResult = await database.get(
                'SELECT COUNT(*) as count FROM users WHERE last_login IS NOT NULL'
            );

            // Get total bookings (if bookings table exists)
            let totalBookings = 0;
            try {
                const bookingsResult = await database.get('SELECT COUNT(*) as count FROM bookings');
                totalBookings = bookingsResult?.count || 0;
            } catch (error) {
                // Bookings table might not exist yet
                console.log('Bookings table not found, setting count to 0');
            }

            return {
                totalUsers: totalUsersResult?.count || 0,
                newUsersToday: newUsersTodayResult?.count || 0,
                totalBookings: totalBookings,
                activeUsers: activeUsersResult?.count || 0
            };
        } catch (error) {
            console.error('Error getting stats:', error);
            throw error;
        }
    }

    // Get all users for admin
    static async getAllUsers() {
        try {
            const users = await database.all(`
                SELECT
                    id,
                    first_name,
                    last_name,
                    email,
                    phone,
                    created_at,
                    last_login,
                    1 as is_active
                FROM users
                ORDER BY created_at DESC
            `);
            return users;
        } catch (error) {
            console.error('Error getting all users:', error);
            throw error;
        }
    }

    // Delete user
    static async deleteUser(userId) {
        try {
            await database.run('DELETE FROM users WHERE id = ?', [userId]);
        } catch (error) {
            console.error('Error deleting user:', error);
            throw error;
        }
    }

    // Get recent activity
    static async getRecentActivity() {
        try {
            // Get recent user registrations
            const recentUsers = await database.all(`
                SELECT
                    'signup' as type,
                    'New User Registration' as title,
                    first_name || ' ' || last_name || ' joined Sports Malta' as description,
                    created_at
                FROM users
                ORDER BY created_at DESC
                LIMIT 10
            `);

            // Get recent logins
            const recentLogins = await database.all(`
                SELECT
                    'login' as type,
                    'User Login' as title,
                    first_name || ' ' || last_name || ' logged in' as description,
                    last_login as created_at
                FROM users
                WHERE last_login IS NOT NULL
                ORDER BY last_login DESC
                LIMIT 10
            `);

            // Combine and sort activities
            const activities = [...recentUsers, ...recentLogins]
                .filter(activity => activity.created_at)
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                .slice(0, 20);

            return activities;
        } catch (error) {
            console.error('Error getting recent activity:', error);
            return [];
        }
    }
}

module.exports = User;
