# Design Document

## Overview

The Sirens booking functionality will transform the existing static booking page into a fully functional system that integrates with the backend database. The system will handle user authentication, real-time availability checking, booking creation, and conflict prevention. The design leverages the existing database schema and builds upon the current frontend structure.

The system follows a client-server architecture where the frontend JavaScript communicates with Express.js API endpoints to manage bookings. The design emphasizes real-time updates, data validation, and user experience optimization.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Frontend - Sirens Booking Page] --> B[Express.js API Server]
    B --> C[PostgreSQL Database]
    B --> D[Authentication Middleware]
    B --> E[Booking Service]
    E --> F[Availability Service]
    E --> G[Validation Service]
    
    subgraph "Database Tables"
        C --> H[users]
        C --> I[facilities]
        C --> J[bookings]
        C --> K[availability]
    end
```

### Request Flow

1. **Page Load**: Frontend loads and authenticates user, fetches initial availability
2. **Date Selection**: User selects date, frontend requests available time slots
3. **Time/Pitch Selection**: User selects time and pitch configuration
4. **Form Submission**: User fills form and submits booking request
5. **Validation**: Server validates all input and checks availability
6. **Booking Creation**: Server creates booking record and updates availability
7. **Response**: Server returns confirmation or error to frontend

## Components and Interfaces

### Frontend Components

#### 1. Authentication Handler
- **Purpose**: Verify user authentication status
- **Methods**:
  - `checkAuthStatus()`: Verify if user is logged in
  - `redirectToLogin()`: Redirect unauthenticated users
  - `getUserInfo()`: Get current user details

#### 2. Availability Manager
- **Purpose**: Handle availability fetching and caching
- **Methods**:
  - `fetchAvailability(date, facilityId)`: Get availability for specific date
  - `updateAvailabilityDisplay(slots)`: Update UI with availability data
  - `cacheAvailability(date, data)`: Cache availability data locally

#### 3. Booking Form Handler
- **Purpose**: Manage form validation and submission
- **Methods**:
  - `validateForm()`: Validate all form fields
  - `submitBooking(bookingData)`: Submit booking to server
  - `handleBookingResponse(response)`: Process server response
  - `resetForm()`: Reset form after successful booking

#### 4. Pitch Selection Manager
- **Purpose**: Handle pitch configuration selection logic
- **Methods**:
  - `selectPitchOption(option)`: Handle full/half pitch selection
  - `updatePitchDisplay()`: Update visual pitch representation
  - `validatePitchAvailability(date, time, pitch)`: Check if selected configuration is available

### Backend Components

#### 1. Authentication Middleware
- **File**: `server/middleware/auth.js`
- **Purpose**: Verify JWT tokens and user sessions
- **Methods**:
  - `verifyToken(req, res, next)`: Verify JWT token
  - `requireAuth(req, res, next)`: Ensure user is authenticated

#### 2. Booking Controller
- **File**: `server/controllers/bookingController.js`
- **Purpose**: Handle booking-related HTTP requests
- **Methods**:
  - `getAvailability(req, res)`: Return availability for date/facility
  - `createBooking(req, res)`: Create new booking
  - `validateBookingData(data)`: Validate booking request data

#### 3. Booking Service
- **File**: `server/services/bookingService.js`
- **Purpose**: Business logic for booking operations
- **Methods**:
  - `checkAvailability(facilityId, date, time, pitchConfig)`: Check if slot is available
  - `createBookingRecord(bookingData)`: Create booking in database
  - `generateBookingReference()`: Generate unique booking reference
  - `updateAvailabilityCache(facilityId, date)`: Update availability cache

#### 4. Facility Service
- **File**: `server/services/facilityService.js`
- **Purpose**: Handle facility-related operations
- **Methods**:
  - `getFacilityById(id)`: Get facility details
  - `getSirensFootballFacility()`: Get Sirens FC football facility
  - `calculateBookingCost(facility, duration, date, time)`: Calculate booking cost

## Data Models

### Booking Request Model
```javascript
{
  facilityId: "uuid",
  date: "YYYY-MM-DD",
  startTime: "HH:MM",
  endTime: "HH:MM",
  pitchConfiguration: "full|left|right",
  userDetails: {
    fullName: "string",
    email: "string",
    phone: "string",
    notes: "string"
  }
}
```

### Availability Response Model
```javascript
{
  date: "YYYY-MM-DD",
  facilityId: "uuid",
  timeSlots: [
    {
      time: "HH:MM",
      duration: 60,
      availability: {
        full: boolean,
        left: boolean,
        right: boolean
      },
      pricing: {
        full: number,
        half: number
      }
    }
  ]
}
```

### Booking Response Model
```javascript
{
  success: boolean,
  booking: {
    id: "uuid",
    reference: "string",
    date: "YYYY-MM-DD",
    startTime: "HH:MM",
    endTime: "HH:MM",
    pitchConfiguration: "string",
    totalCost: number,
    status: "confirmed"
  },
  message: "string"
}
```

## Database Schema Extensions

The existing schema supports the booking functionality, but we need to clarify how pitch configurations are handled:

### Pitch Configuration Storage
- **Full Pitch**: Single booking record with `special_requests` indicating "full pitch"
- **Half Pitch**: Booking record with `special_requests` indicating "left side" or "right side"
- **Conflict Detection**: Query existing bookings for same facility, date, and time to check for conflicts

### Booking Reference Generation
- Format: `SFC-YYYYMMDD-HHMMSS-XXX` (e.g., `SFC-20250728-143000-001`)
- Components: Venue prefix + Date + Time + Sequential number

## Error Handling

### Frontend Error Handling
1. **Network Errors**: Display user-friendly messages for connection issues
2. **Validation Errors**: Highlight invalid fields with specific error messages
3. **Booking Conflicts**: Show alternative available slots when conflicts occur
4. **Authentication Errors**: Redirect to login with return URL

### Backend Error Handling
1. **Validation Errors**: Return 400 with detailed field-level errors
2. **Authentication Errors**: Return 401 with clear authentication requirements
3. **Booking Conflicts**: Return 409 with current availability data
4. **Database Errors**: Return 500 with generic error message, log detailed error

### Error Response Format
```javascript
{
  success: false,
  error: {
    code: "BOOKING_CONFLICT",
    message: "Selected time slot is no longer available",
    details: {
      field: "timeSlot",
      availableAlternatives: [...]
    }
  }
}
```

## Testing Strategy

### Unit Tests
1. **Frontend Components**:
   - Form validation logic
   - Date/time selection handling
   - Pitch configuration logic
   - API request/response handling

2. **Backend Services**:
   - Booking creation logic
   - Availability checking algorithms
   - Conflict detection
   - Cost calculation

### Integration Tests
1. **API Endpoints**:
   - Authentication flow
   - Booking creation end-to-end
   - Availability fetching
   - Error handling scenarios

2. **Database Operations**:
   - Booking record creation
   - Availability cache updates
   - Concurrent booking scenarios

### End-to-End Tests
1. **Complete Booking Flow**:
   - User authentication
   - Date/time selection
   - Form submission
   - Booking confirmation

2. **Conflict Scenarios**:
   - Simultaneous booking attempts
   - Availability updates
   - Error recovery

### Test Data Setup
- Use test database with sample Sirens FC facility
- Create test users with different authentication states
- Generate various booking scenarios for conflict testing

## Security Considerations

### Authentication
- JWT token validation on all booking endpoints
- Session timeout handling
- Secure token storage in frontend

### Data Validation
- Server-side validation of all booking data
- SQL injection prevention using parameterized queries
- Input sanitization for user-provided data

### Rate Limiting
- Implement rate limiting on booking endpoints
- Prevent spam booking attempts
- Monitor for suspicious activity patterns

## Performance Optimization

### Caching Strategy
- Cache availability data for frequently requested dates
- Implement Redis caching for high-traffic periods
- Use browser caching for static availability data

### Database Optimization
- Use database indexes for booking queries
- Implement connection pooling
- Optimize availability queries with proper indexing

### Frontend Optimization
- Debounce availability requests during date selection
- Implement loading states for better user experience
- Cache facility data to reduce API calls