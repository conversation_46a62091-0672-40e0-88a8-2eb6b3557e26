document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const showRegisterLink = document.getElementById('showRegister');
    const showLoginLink = document.getElementById('showLogin');
    const passwordToggles = document.querySelectorAll('.password-toggle');
    const messageContainer = document.getElementById('messageContainer');
    const messageElement = document.getElementById('message');

    // Toggle password visibility
    function togglePasswordVisibility(input, button) {
        const icon = button.querySelector('i');
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    }

    // Show message to user
    function showMessage(message, type = 'info') {
        if (messageElement && messageContainer) {
            messageElement.textContent = message;
            messageContainer.className = `message-container ${type}`;
            messageContainer.style.display = 'block';
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    messageContainer.style.display = 'none';
                }, 5000);
            }
        }
    }

    // Handle login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email')?.value.trim();
            const password = document.getElementById('password')?.value;
            const rememberMe = document.getElementById('rememberMe')?.checked;
            let originalBtnText = 'Sign In';
            let submitBtn;
            
            try {
                // Show loading state
                submitBtn = loginForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    originalBtnText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
                }
                
                // Simulate API call (replace with actual API call)
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // For demo purposes - in a real app, this would be an actual API call
                if (email && password) {
                    // Save user to localStorage
                    const user = { email, name: email.split('@')[0] };
                    localStorage.setItem('currentUser', JSON.stringify(user));
                    
                    // Show success message
                    showMessage('Login successful! Redirecting...', 'success');
                    
                    // Redirect to home page after a short delay
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    throw new Error('Please enter both email and password');
                }
            } catch (error) {
                showMessage(error.message || 'Login failed. Please try again.', 'error');
            } finally {
                // Reset button state
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalBtnText;
                }
            }
        });
    }
    
    // Toggle between login and register forms
    if (showRegisterLink && showLoginLink) {
        showRegisterLink.addEventListener('click', function(e) {
            e.preventDefault();
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            if (loginForm) loginForm.classList.add('hidden');
            if (registerForm) registerForm.classList.remove('hidden');
            showRegisterLink.classList.add('hidden');
            showLoginLink.classList.remove('hidden');
        });
        
        showLoginLink.addEventListener('click', function(e) {
            e.preventDefault();
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            if (registerForm) registerForm.classList.add('hidden');
            if (loginForm) loginForm.classList.remove('hidden');
            showLoginLink.classList.add('hidden');
            showRegisterLink.classList.remove('hidden');
        });
    }
    
    // Initialize password toggles
    if (passwordToggles) {
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const input = this.previousElementSibling;
                if (input && input.tagName === 'INPUT') {
                    togglePasswordVisibility(input, this);
                }
            });
        });
    }
    
    // Check if user is already logged in
    function checkAuthStatus() {
        const user = localStorage.getItem('currentUser');
        if (user) {
            // User is logged in, redirect to home page
            window.location.href = 'index.html';
        }
    }
    
    // Initialize auth status check
    checkAuthStatus();
});
