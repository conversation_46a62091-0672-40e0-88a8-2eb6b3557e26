/**
 * Authentication Handler
 * Manages user authentication, login, signup, and session management
 */
class AuthHandler {
    constructor() {
        this.apiBaseUrl = '/api';
        this.token = localStorage.getItem('authToken');
        this.user = null;
        this.csrfToken = null;
        this.init();
    }

    async init() {
        // Fetch CSRF token
        await this.fetchCSRFToken();

        // Check if user is authenticated on page load
        if (this.token) {
            await this.verifyToken();
        }
    }

    async fetchCSRFToken() {
        // CSRF token disabled for development
        console.log('📧 CSRF token disabled for development');
        return;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.token && this.user;
    }

    // Get current user
    getCurrentUser() {
        return this.user;
    }

    // Verify token with server
    async verifyToken() {
        if (!this.token) return { success: false };

        try {
            const response = await fetch(`${this.apiBaseUrl}/verify`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.user = data.user;
                return { success: true, user: this.user };
            } else {
                this.logout();
                return { success: false };
            }
        } catch (error) {
            console.error('Token verification failed:', error);
            this.logout();
            return { success: false };
        }
    }

    // Check authentication status
    async checkAuthStatus() {
        return await this.verifyToken();
    }

    // Login user
    async login(email, password, rememberMe = false) {
        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            const response = await fetch(`${this.apiBaseUrl}/login`, {
                method: 'POST',
                headers,
                credentials: 'include',
                body: JSON.stringify({ email, password })
            });

            const data = await response.json();

            if (data.success) {
                this.token = data.token;
                this.user = data.user;

                // Store token
                if (rememberMe) {
                    localStorage.setItem('authToken', this.token);
                } else {
                    sessionStorage.setItem('authToken', this.token);
                }

                // Dispatch auth state change event
                document.dispatchEvent(new CustomEvent('authStateChanged', { detail: { user: this.user } }));

                return { success: true, user: this.user };
            } else {
                return { success: false, error: data.error };
            }
        } catch (error) {
            console.error('Login failed:', error);
            return { success: false, error: { message: 'Network error occurred' } };
        }
    }

    // Signup user
    async signup(userData) {
        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            const response = await fetch(`${this.apiBaseUrl}/register`, {
                method: 'POST',
                headers,
                credentials: 'include',
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.success) {
                // Registration successful - redirect to login page
                return { success: true, message: data.message };
            } else {
                return { success: false, error: data.error || { message: data.message || 'Registration failed' } };
            }
        } catch (error) {
            console.error('Signup failed:', error);
            return { success: false, error: { message: 'Network error occurred' } };
        }
    }

    // Logout user
    async logout() {
        try {
            if (this.token) {
                await fetch(`${this.apiBaseUrl}/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('Logout request failed:', error);
        } finally {
            // Clear local data
            this.token = null;
            this.user = null;
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('authToken');

            // Dispatch auth state change event
            document.dispatchEvent(new CustomEvent('authStateChanged', { detail: { user: null } }));
        }
    }

    // Redirect to login page
    redirectToLogin(returnUrl = null) {
        const url = returnUrl ? `/public/login.html?returnUrl=${encodeURIComponent(returnUrl)}` : '/public/login.html';
        window.location.href = url;
    }

    // Make authenticated API request
    async makeAuthenticatedRequest(url, options = {}) {
        if (!this.token) {
            this.redirectToLogin();
            return null;
        }

        // Ensure we have a CSRF token for non-GET requests
        if (!this.csrfToken && options.method && options.method !== 'GET') {
            await this.fetchCSRFToken();
        }

        const headers = {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
            ...options.headers
        };

        // Add CSRF token for non-GET requests
        if (this.csrfToken && options.method && options.method !== 'GET') {
            headers['X-CSRF-Token'] = this.csrfToken;
        }

        try {
            const response = await fetch(url, {
                ...options,
                headers,
                credentials: 'include' // Include cookies for session
            });

            if (response.status === 401) {
                // Token expired or invalid
                this.logout();
                this.redirectToLogin();
                return null;
            }

            if (response.status === 403) {
                // CSRF token might be invalid, refresh it
                await this.fetchCSRFToken();

                // Retry the request once with new CSRF token
                if (this.csrfToken && options.method && options.method !== 'GET') {
                    headers['X-CSRF-Token'] = this.csrfToken;
                    return await fetch(url, {
                        ...options,
                        headers,
                        credentials: 'include'
                    });
                }
            }

            return response;
        } catch (error) {
            console.error('Authenticated request failed:', error);
            throw error;
        }
    }

    // Initialize login form
    initLoginForm() {
        const form = document.getElementById('loginForm');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const passwordToggle = document.getElementById('passwordToggle');
        const rememberMeInput = document.getElementById('rememberMe');
        const submitButton = document.getElementById('loginButton');
        const formError = document.getElementById('form-error');

        if (!form) return;

        // Password toggle functionality
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                const type = passwordInput.type === 'password' ? 'text' : 'password';
                passwordInput.type = type;
                passwordToggle.querySelector('.password-toggle-icon').textContent = type === 'password' ? '👁️' : '🙈';
            });
        }

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const rememberMe = rememberMeInput ? rememberMeInput.checked : false;

            // Clear previous errors
            this.clearFormErrors();

            // Validate inputs
            if (!email || !password) {
                this.showFormError('Please fill in all fields');
                return;
            }

            // Show loading state
            this.setButtonLoading(submitButton, true);

            try {
                const result = await this.login(email, password, rememberMe);

                if (result.success) {
                    // Get return URL from query params
                    const urlParams = new URLSearchParams(window.location.search);
                    const returnUrl = urlParams.get('returnUrl') || '/public/account.html';

                    // Redirect to return URL or account page
                    window.location.href = returnUrl;
                } else {
                    this.showFormError(result.error.message || 'Login failed');
                }
            } catch (error) {
                this.showFormError('An error occurred. Please try again.');
            } finally {
                this.setButtonLoading(submitButton, false);
            }
        });
    }

    // Initialize signup form
    initSignupForm() {
        const form = document.getElementById('signupForm');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const passwordToggle = document.getElementById('passwordToggle');
        const confirmPasswordToggle = document.getElementById('confirmPasswordToggle');
        const submitButton = document.getElementById('signupButton');

        if (!form) return;

        // Password toggle functionality
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                const type = passwordInput.type === 'password' ? 'text' : 'password';
                passwordInput.type = type;
                passwordToggle.querySelector('.password-toggle-icon').textContent = type === 'password' ? '👁️' : '🙈';
            });
        }

        if (confirmPasswordToggle) {
            confirmPasswordToggle.addEventListener('click', () => {
                const type = confirmPasswordInput.type === 'password' ? 'text' : 'password';
                confirmPasswordInput.type = type;
                confirmPasswordToggle.querySelector('.password-toggle-icon').textContent = type === 'password' ? '👁️' : '🙈';
            });
        }

        // Password validation
        if (passwordInput) {
            passwordInput.addEventListener('input', () => {
                this.validatePassword(passwordInput.value);
            });
        }

        // Confirm password validation
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', () => {
                this.validatePasswordMatch(passwordInput.value, confirmPasswordInput.value);
            });
        }

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const firstName = formData.get('firstName').trim();
            const lastName = formData.get('lastName').trim();
            const confirmPassword = formData.get('confirmPassword');

            const userData = {
                name: `${firstName} ${lastName}`,
                email: formData.get('email').trim(),
                password: formData.get('password'),
                confirmPassword: confirmPassword
            };

            // Clear previous errors
            this.clearFormErrors();

            // Validate inputs
            if (!this.validateSignupForm(userData, confirmPassword)) {
                return;
            }

            // Show loading state
            this.setButtonLoading(submitButton, true);

            try {
                const result = await this.signup(userData);

                if (result.success) {
                    // Show success message and redirect to login form
                    alert('Account created successfully! Please log in to continue.');
                    // Toggle to login form if toggle function exists
                    if (window.enhancedLogin && window.enhancedLogin.toggleForms) {
                        window.enhancedLogin.toggleForms('login');
                    } else {
                        // Fallback: reload page to show login form
                        window.location.reload();
                    }
                } else {
                    this.showFormError(result.error.message || 'Signup failed');
                }
            } catch (error) {
                this.showFormError('An error occurred. Please try again.');
            } finally {
                this.setButtonLoading(submitButton, false);
            }
        });
    }

    // Validate password strength
    validatePassword(password) {
        const requirements = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password)
        };

        // Update requirement indicators
        Object.keys(requirements).forEach(req => {
            const element = document.getElementById(`${req}-req`);
            if (element) {
                element.className = `password-requirement ${requirements[req] ? 'valid' : 'invalid'}`;
            }
        });

        return Object.values(requirements).every(Boolean);
    }

    // Validate password match
    validatePasswordMatch(password, confirmPassword) {
        const isMatch = password === confirmPassword;
        const errorElement = document.getElementById('confirmPassword-error');
        
        if (confirmPassword && !isMatch) {
            this.showFieldError('confirmPassword', 'Passwords do not match');
        } else {
            this.clearFieldError('confirmPassword');
        }

        return isMatch;
    }

    // Validate signup form
    validateSignupForm(userData, confirmPassword) {
        let isValid = true;

        // Check required fields (excluding confirmPassword as it's handled separately)
        const requiredFields = ['name', 'email', 'password'];
        requiredFields.forEach(field => {
            if (!userData[field]) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            }
        });

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (userData.email && !emailRegex.test(userData.email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            isValid = false;
        }

        // Validate password
        if (!this.validatePassword(userData.password)) {
            this.showFieldError('password', 'Password does not meet requirements');
            isValid = false;
        }

        // Validate password match
        if (!this.validatePasswordMatch(userData.password, confirmPassword)) {
            isValid = false;
        }

        return isValid;
    }

    // Show form error
    showFormError(message) {
        const errorElement = document.getElementById('form-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }

    // Show field error
    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }

    // Clear field error
    clearFieldError(fieldName) {
        const errorElement = document.getElementById(`${fieldName}-error`);
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        }
    }

    // Clear all form errors
    clearFormErrors() {
        const errorElements = document.querySelectorAll('.form-error');
        errorElements.forEach(element => {
            element.textContent = '';
            element.classList.remove('show');
        });
    }

    // Set button loading state
    setButtonLoading(button, isLoading) {
        if (!button) return;

        const textElement = button.querySelector('.btn-text');
        const loadingElement = button.querySelector('.btn-loading');

        if (isLoading) {
            button.disabled = true;
            if (textElement) textElement.style.display = 'none';
            if (loadingElement) loadingElement.style.display = 'flex';
        } else {
            button.disabled = false;
            if (textElement) textElement.style.display = 'inline';
            if (loadingElement) loadingElement.style.display = 'none';
        }
    }
}

// Initialize auth handler
window.authHandler = new AuthHandler();
