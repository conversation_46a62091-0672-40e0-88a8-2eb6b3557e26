const nodemailer = require('nodemailer');
require('dotenv').config();

class EmailService {
    constructor() {
        this.transporter = null;
        this.initializeTransporter();
    }

    // Initialize email transporter
    initializeTransporter() {
        // For development, you can use Gmail SMTP or other email services
        // For production, consider using services like SendGrid, AWS SES, etc.

        if (process.env.EMAIL_SERVICE === 'gmail') {
            console.log('🔧 Initializing Gmail SMTP transporter...');
            this.transporter = nodemailer.createTransport({
                service: 'gmail',
                host: 'smtp.gmail.com',
                port: 587,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: process.env.EMAIL_USER,
                    pass: process.env.EMAIL_PASSWORD // Use App Password for Gmail
                },
                tls: {
                    rejectUnauthorized: false
                },
                connectionTimeout: 60000, // 60 seconds
                greetingTimeout: 30000, // 30 seconds
                socketTimeout: 60000, // 60 seconds
                debug: process.env.NODE_ENV === 'development',
                logger: process.env.NODE_ENV === 'development'
            });
        } else if (process.env.EMAIL_SERVICE === 'smtp') {
            console.log('🔧 Initializing custom SMTP transporter...');
            this.transporter = nodemailer.createTransport({
                host: process.env.SMTP_HOST,
                port: process.env.SMTP_PORT || 587,
                secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
                auth: {
                    user: process.env.SMTP_USER,
                    pass: process.env.SMTP_PASSWORD
                },
                tls: {
                    rejectUnauthorized: false
                },
                connectionTimeout: 60000,
                greetingTimeout: 30000,
                socketTimeout: 60000
            });
        } else {
            console.log('🔧 Initializing test email transporter...');
            // Fallback to Ethereal Email for testing (creates test accounts)
            this.initializeTestTransporter();
        }
    }

    // Initialize test email transporter (for development)
    async initializeTestTransporter() {
        try {
            const testAccount = await nodemailer.createTestAccount();
            
            this.transporter = nodemailer.createTransport({
                host: 'smtp.ethereal.email',
                port: 587,
                secure: false,
                auth: {
                    user: testAccount.user,
                    pass: testAccount.pass
                }
            });

            console.log('📧 Test email account created:');
            console.log('   User:', testAccount.user);
            console.log('   Pass:', testAccount.pass);
            console.log('   Preview emails at: https://ethereal.email');
        } catch (error) {
            console.error('Failed to create test email account:', error);
        }
    }

    // Generate password reset email HTML
    generatePasswordResetEmailHTML(userName, resetUrl) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Reset Your Password - With Love</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .logo { max-width: 150px; margin-bottom: 20px; }
                    .button {
                        display: inline-block; 
                        padding: 12px 24px; 
                        background-color: #e91e63; 
                        color: white; 
                        text-decoration: none; 
                        border-radius: 4px; 
                        margin: 20px 0;
                    }
                    .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>With Love</h1>
                    <h2>Reset Your Password</h2>
                </div>
                
                <p>Hello ${userName || 'there'},</p>
                
                <p>We received a request to reset your password for your With Love account. If you didn't make this request, you can safely ignore this email.</p>
                
                <p>To reset your password, please click the button below:</p>
                
                <p style="text-align: center;">
                    <a href="${resetUrl}" class="button">Reset Password</a>
                </p>
                
                <p>Or copy and paste this link into your browser:</p>
                <p>${resetUrl}</p>
                
                <p>This link will expire in 1 hour for security reasons.</p>
                
                <p>If you have any questions, please don't hesitate to contact our support team.</p>
                
                <p>With love,<br>The With Love Team</p>
                
                <div class="footer">
                    <p>© ${new Date().getFullYear()} With Love. All rights reserved.</p>
                    <p>If you didn't request this email, you can safely ignore it.</p>
                </div>
            </body>
            </html>
        `;
    }

    // Generate password reset email text
    generatePasswordResetEmailText(userName, resetUrl) {
        return `
            With Love - Reset Your Password
            =============================
            
            Hello ${userName || 'there'},
            
            We received a request to reset your password for your With Love account. 
            If you didn't make this request, you can safely ignore this email.
            
            To reset your password, please visit the following link:
            ${resetUrl}
            
            This link will expire in 1 hour for security reasons.
            
            If you have any questions, please don't hesitate to contact our support team.
            
            With love,
            The With Love Team
            
            -----------------------------
            © ${new Date().getFullYear()} With Love. All rights reserved.
            If you didn't request this email, you can safely ignore it.
        `;
    }

    // Send password reset email
    async sendPasswordResetEmail(userEmail, userName, resetUrl, retryCount = 0) {
        const maxRetries = 3;

        try {
            // Verify transporter before sending
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized, reinitializing...');
                this.initializeTransporter();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified for password reset email');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendPasswordResetEmail(userEmail, userName, resetUrl, retryCount + 1);
                }
                throw verifyError;
            }

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: userEmail,
                subject: '🔑 Reset Your Password - With Love',
                html: this.generatePasswordResetEmailHTML(userName, resetUrl),
                text: this.generatePasswordResetEmailText(userName, resetUrl)
            };

            console.log(`📤 Sending password reset email to: ${userEmail}`);
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ Password reset email sent successfully to:', userEmail);
            console.log('📧 Message ID:', info.messageId);

            if (info.previewURL) {
                console.log('🔗 Preview URL:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send password reset email (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendPasswordResetEmail(userEmail, userName, resetUrl, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Send welcome email to new users
    async sendWelcomeEmail(userEmail, userName, retryCount = 0) {
        const maxRetries = 3;

        try {
            // Verify transporter before sending
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized, reinitializing...');
                this.initializeTransporter();

                // Wait a moment for initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);

                // If Gmail SMTP fails, try fallback to test email
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendWelcomeEmail(userEmail, userName, retryCount + 1);
                }

                throw verifyError;
            }

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: userEmail,
                subject: '🎉 Welcome to With Love - Julia\'s Magnets!',
                html: this.generateWelcomeEmailHTML(userName),
                text: this.generateWelcomeEmailText(userName)
            };

            console.log(`📤 Sending welcome email to: ${userEmail}`);
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ Welcome email sent successfully to:', userEmail);
            console.log('📧 Message ID:', info.messageId);

            // If using Ethereal Email, log the preview URL
            if (info.previewURL) {
                console.log('🔗 Preview URL:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send welcome email (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            // Retry logic for network errors
            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendWelcomeEmail(userEmail, userName, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Send email verification email to new users
    async sendVerificationEmail(userEmail, userName, verificationToken, retryCount = 0) {
        const maxRetries = 3;

        try {
            // Verify transporter before sending
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized, reinitializing...');
                this.initializeTransporter();

                // Wait a moment for initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified for verification email');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);

                // If Gmail SMTP fails, try fallback to test email
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendVerificationEmail(userEmail, userName, verificationToken, retryCount + 1);
                }

                throw verifyError;
            }

            const verificationUrl = `http://localhost:3000/verify-email?token=${verificationToken}`;

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: userEmail,
                subject: '🔐 Verify Your Email Address - Julia\'s Custom Magnets',
                html: `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Verify Your Email Address</title>
                        <style>
                            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
                            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                            .header { background: linear-gradient(135deg, #ff6b8b, #ff8fab); color: white; padding: 40px 30px; text-align: center; }
                            .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
                            .content { padding: 40px 30px; }
                            .verification-box { background: #f8f9fa; border-radius: 12px; padding: 30px; text-align: center; margin: 25px 0; border-left: 4px solid #ff6b8b; }
                            .verify-button { display: inline-block; background: linear-gradient(135deg, #ff6b8b, #ff8fab); color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; transition: transform 0.2s; }
                            .verify-button:hover { transform: translateY(-2px); }
                            .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef; }
                            .footer p { margin: 5px 0; color: #666; font-size: 14px; }
                            .logo { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                            .security-note { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0; color: #856404; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <div class="logo">🧲 Julia's Custom Magnets</div>
                                <h1>Verify Your Email Address</h1>
                            </div>
                            <div class="content">
                                <p>Hello <strong>${userName}</strong>,</p>

                                <p>Welcome to Julia's Custom Magnets! We're excited to have you join our community of magnet lovers.</p>

                                <div class="verification-box">
                                    <h3>🔐 Email Verification Required</h3>
                                    <p>To complete your registration and start creating custom magnets, please verify your email address by clicking the button below:</p>

                                    <a href="${verificationUrl}" class="verify-button">
                                        ✅ Verify My Email Address
                                    </a>
                                </div>

                                <div class="security-note">
                                    <strong>🛡️ Security Note:</strong> This verification link will expire in 24 hours for your security. If you didn't create an account with us, please ignore this email.
                                </div>

                                <p>Once verified, you'll be able to:</p>
                                <ul>
                                    <li>🎨 Create custom magnets with your own images</li>
                                    <li>🛒 Place orders and track your purchases</li>
                                    <li>📧 Receive order updates and special offers</li>
                                    <li>💝 Access exclusive member benefits</li>
                                </ul>

                                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                                <p style="word-break: break-all; color: #666; font-size: 14px;">${verificationUrl}</p>

                                <p>Best regards,<br>
                                <strong>Julia's Custom Magnets Team</strong></p>
                            </div>
                            <div class="footer">
                                <p><strong>Julia's Custom Magnets</strong></p>
                                <p>Creating beautiful custom magnets with love! 💕</p>
                                <p>Visit us at: <a href="http://localhost:3000" style="color: #ff6b8b;">Julia's Custom Magnets</a></p>
                                <p style="font-size: 12px; color: #999; margin-top: 15px;">
                                    This email was sent to ${userEmail}. If you didn't sign up for an account, please ignore this email.
                                </p>
                            </div>
                        </div>
                    </body>
                    </html>
                `,
                text: `Hello ${userName},

Welcome to Julia's Custom Magnets!

To complete your registration, please verify your email address by visiting this link:
${verificationUrl}

This verification link will expire in 24 hours for your security.

Once verified, you'll be able to create custom magnets, place orders, and access all member benefits.

If you didn't create an account with us, please ignore this email.

Best regards,
Julia's Custom Magnets Team

Visit us at: http://localhost:3000`
            };

            const info = await this.transporter.sendMail(mailOptions);
            console.log(`✅ Verification email sent to ${userEmail}`);

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send verification email (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            // Retry logic for network errors
            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendVerificationEmail(userEmail, userName, verificationToken, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Send newsletter welcome email to new subscribers
    async sendNewsletterWelcomeEmail(userEmail, retryCount = 0) {
        const maxRetries = 3;

        try {
            // Verify transporter before sending
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized, reinitializing...');
                this.initializeTransporter();

                // Wait a moment for initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified for newsletter email');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);

                // If Gmail SMTP fails, try fallback to test email
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendNewsletterWelcomeEmail(userEmail, retryCount + 1);
                }

                throw verifyError;
            }

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: userEmail,
                subject: '💌 Welcome to our Newsletter - Julia\'s Magnets!',
                html: this.generateNewsletterWelcomeEmailHTML(userEmail),
                text: this.generateNewsletterWelcomeEmailText(userEmail)
            };

            console.log(`📤 Sending newsletter welcome email to: ${userEmail}`);
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ Newsletter welcome email sent successfully to:', userEmail);
            console.log('📧 Message ID:', info.messageId);

            // If using Ethereal Email, log the preview URL
            if (info.previewURL) {
                console.log('🔗 Preview URL:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send newsletter welcome email (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            // Retry logic for network errors
            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendNewsletterWelcomeEmail(userEmail, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Generate HTML version of welcome email
    generateWelcomeEmailHTML(userName) {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to With Love - Julia's Magnets</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }
                .email-container {
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #f0d9d9;
                }
                .logo {
                    font-size: 2.5em;
                    color: #ff6b8b;
                    margin-bottom: 10px;
                }
                .welcome-title {
                    color: #a67f83;
                    font-size: 1.8em;
                    margin: 0;
                }
                .content {
                    margin: 20px 0;
                }
                .highlight {
                    color: #ff6b8b;
                    font-weight: bold;
                }
                .features {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                }
                .features ul {
                    margin: 0;
                    padding-left: 20px;
                }
                .features li {
                    margin: 8px 0;
                    color: #555;
                }
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #ff6b8b, #ff8fab);
                    color: white;
                    padding: 12px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    margin: 20px 0;
                    text-align: center;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">❤️</div>
                    <h1 class="welcome-title">Welcome to With Love</h1>
                    <p>Julia's Handmade Magnets</p>
                </div>
                
                <div class="content">
                    <p>Dear <span class="highlight">${userName}</span>,</p>
                    
                    <p>Welcome to our beautiful community! We're absolutely <strong>thrilled</strong> to have you join the With Love family. 🎉</p>
                    
                    <p>At With Love, every magnet is crafted with care, love, and attention to detail. You're now part of a special community that appreciates handmade treasures and unique designs.</p>
                    
                    <div class="features">
                        <h3>What you can look forward to:</h3>
                        <ul>
                            <li>🎨 <strong>Exclusive designs</strong> - Unique magnets you won't find anywhere else</li>
                            <li>💝 <strong>Personalized options</strong> - Custom magnets for special occasions</li>
                            <li>🚚 <strong>Easy ordering</strong> - Simple and secure checkout process</li>
                            <li>❤️ <strong>Made with love</strong> - Each piece crafted with care and attention</li>
                            <li>📧 <strong>Special offers</strong> - Exclusive deals for our community members</li>
                        </ul>
                    </div>
                    
                    <p>Ready to explore our collection? Start browsing our beautiful magnets and find the perfect pieces for your home, office, or as gifts for loved ones.</p>
                    
                    <div style="text-align: center;">
                        <a href="${process.env.WEBSITE_URL || 'http://localhost:3000'}/Html%20pages/shop.html" class="cta-button">
                            Start Shopping ❤️
                        </a>
                    </div>
                    
                    <p>If you have any questions or need assistance, don't hesitate to reach out. We're here to help make your experience wonderful!</p>
                    
                    <p>With love and gratitude,<br>
                    <strong>Julia & The With Love Team</strong> ❤️</p>
                </div>
                
                <div class="footer">
                    <p>This email was sent because you created an account at With Love - Julia's Magnets.</p>
                    <p>© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with ❤️</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate HTML version of newsletter welcome email
    generateNewsletterWelcomeEmailHTML(userEmail) {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Our Newsletter - Julia's Magnets</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }
                .email-container {
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #f0d9d9;
                }
                .logo {
                    font-size: 2.5em;
                    color: #ff6b8b;
                    margin-bottom: 10px;
                }
                .newsletter-title {
                    color: #a67f83;
                    font-size: 1.8em;
                    margin: 0;
                }
                .content {
                    margin: 20px 0;
                }
                .highlight {
                    color: #ff6b8b;
                    font-weight: bold;
                }
                .benefits {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border-left: 4px solid #ff6b8b;
                }
                .benefits ul {
                    margin: 0;
                    padding-left: 20px;
                }
                .benefits li {
                    margin: 8px 0;
                    color: #555;
                }
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #ff6b8b, #ff8fab);
                    color: white;
                    padding: 12px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    margin: 20px 0;
                    text-align: center;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 0.9em;
                }
                .unsubscribe {
                    font-size: 0.8em;
                    color: #999;
                    margin-top: 15px;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">💌</div>
                    <h1 class="newsletter-title">Welcome to Our Newsletter!</h1>
                    <p>Julia's Handmade Magnets</p>
                </div>

                <div class="content">
                    <p>Hello there! 👋</p>

                    <p>Thank you so much for subscribing to our newsletter! We're <strong>absolutely delighted</strong> to have you join our community of magnet lovers. 💕</p>

                    <p>You've just taken the first step into a world of <span class="highlight">handcrafted beauty</span> and <span class="highlight">personalized treasures</span>. Every magnet we create is made with love, care, and attention to detail.</p>

                    <div class="benefits">
                        <h3>🎁 What you can expect from our newsletter:</h3>
                        <ul>
                            <li>🎨 <strong>Exclusive previews</strong> of new designs before anyone else</li>
                            <li>💰 <strong>Special subscriber discounts</strong> and early access to sales</li>
                            <li>🎯 <strong>Personalization tips</strong> and creative ideas for your magnets</li>
                            <li>📖 <strong>Behind-the-scenes stories</strong> of how our magnets are made</li>
                            <li>🎉 <strong>Seasonal collections</strong> and limited edition releases</li>
                            <li>💡 <strong>Gift ideas</strong> and inspiration for special occasions</li>
                        </ul>
                    </div>

                    <p>We promise to only send you content that adds value to your day and never spam your inbox. Quality over quantity is our motto! ✨</p>

                    <div style="text-align: center;">
                        <a href="${process.env.WEBSITE_URL || 'http://localhost:3000'}/Html%20pages/shop.html" class="cta-button">
                            Explore Our Collection 🛍️
                        </a>
                    </div>

                    <p>Have questions or want to share your magnet ideas with us? Simply reply to this email - we love hearing from our community!</p>

                    <p>With love and excitement,<br>
                    <strong>Julia & The With Love Team</strong> ❤️</p>
                </div>

                <div class="footer">
                    <p>You're receiving this email because you subscribed to our newsletter at With Love - Julia's Magnets.</p>
                    <p>© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with ❤️</p>
                    <div class="unsubscribe">
                        <p>Want to unsubscribe? <a href="mailto:${process.env.EMAIL_FROM}?subject=Unsubscribe%20Request" style="color: #ff6b8b;">Let us know</a> and we'll take care of it right away.</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate plain text version of newsletter welcome email
    generateNewsletterWelcomeEmailText(userEmail) {
        return `
Welcome to Our Newsletter - Julia's Magnets!

Hello there!

Thank you so much for subscribing to our newsletter! We're absolutely delighted to have you join our community of magnet lovers.

You've just taken the first step into a world of handcrafted beauty and personalized treasures. Every magnet we create is made with love, care, and attention to detail.

What you can expect from our newsletter:
• Exclusive previews of new designs before anyone else
• Special subscriber discounts and early access to sales
• Personalization tips and creative ideas for your magnets
• Behind-the-scenes stories of how our magnets are made
• Seasonal collections and limited edition releases
• Gift ideas and inspiration for special occasions

We promise to only send you content that adds value to your day and never spam your inbox. Quality over quantity is our motto!

Explore our collection: ${process.env.WEBSITE_URL || 'http://localhost:3000'}/Html%20pages/shop.html

Have questions or want to share your magnet ideas with us? Simply reply to this email - we love hearing from our community!

With love and excitement,
Julia & The With Love Team

---
You're receiving this email because you subscribed to our newsletter at With Love - Julia's Magnets.
© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with love.

Want to unsubscribe? Reply with "Unsubscribe" and we'll take care of it right away.
        `;
    }

    // Send newsletter subscription notification to site owner
    async sendNewsletterNotificationEmail(subscriberEmail, retryCount = 0) {
        const maxRetries = 3;
        const ownerEmail = '<EMAIL>';

        try {
            // Verify transporter before sending
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized, reinitializing...');
                this.initializeTransporter();

                // Wait a moment for initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified for newsletter notification');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);

                // If Gmail SMTP fails, try fallback to test email
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendNewsletterNotificationEmail(subscriberEmail, retryCount + 1);
                }

                throw verifyError;
            }

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets Newsletter System',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: ownerEmail,
                subject: '🎉 New Newsletter Subscriber!',
                html: this.generateNewsletterNotificationHTML(subscriberEmail),
                text: this.generateNewsletterNotificationText(subscriberEmail)
            };

            console.log(`📤 Sending newsletter notification to owner: ${ownerEmail}`);
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ Newsletter notification sent successfully to:', ownerEmail);
            console.log('📧 Message ID:', info.messageId);

            // If using Ethereal Email, log the preview URL
            if (info.previewURL) {
                console.log('🔗 Preview URL:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send newsletter notification (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            // Retry logic for network errors
            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendNewsletterNotificationEmail(subscriberEmail, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Generate HTML version of newsletter notification email for owner
    generateNewsletterNotificationHTML(subscriberEmail) {
        const currentDate = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Newsletter Subscriber - Julia's Magnets</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }
                .email-container {
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #f0d9d9;
                }
                .logo {
                    font-size: 2.5em;
                    color: #ff6b8b;
                    margin-bottom: 10px;
                }
                .notification-title {
                    color: #a67f83;
                    font-size: 1.8em;
                    margin: 0;
                }
                .content {
                    margin: 20px 0;
                }
                .highlight {
                    color: #ff6b8b;
                    font-weight: bold;
                    font-size: 1.1em;
                }
                .subscriber-info {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border-left: 4px solid #ff6b8b;
                }
                .date-info {
                    background: #e8f5e8;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 15px 0;
                    border-left: 4px solid #28a745;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">🎉</div>
                    <h1 class="notification-title">New Newsletter Subscriber!</h1>
                    <p>With Love - Julia's Magnets</p>
                </div>

                <div class="content">
                    <p>Hi Julia! 👋</p>

                    <p>Great news! You have a <strong>new subscriber</strong> to your newsletter! 🎊</p>

                    <div class="subscriber-info">
                        <h3>📧 Subscriber Details:</h3>
                        <p><strong>Email:</strong> <span class="highlight">${subscriberEmail}</span></p>
                    </div>

                    <div class="date-info">
                        <p><strong>📅 Subscription Date:</strong> ${currentDate}</p>
                    </div>

                    <p>This person is now part of your growing community and will receive:</p>
                    <ul>
                        <li>🎨 Exclusive design previews</li>
                        <li>💰 Special subscriber discounts</li>
                        <li>🎯 Personalization tips and ideas</li>
                        <li>📖 Behind-the-scenes stories</li>
                        <li>🎉 Seasonal collections updates</li>
                        <li>💡 Gift ideas and inspiration</li>
                    </ul>

                    <p>They've already received a beautiful welcome email introducing them to your brand! 💌</p>

                    <p><strong>Keep up the amazing work!</strong> Your handmade magnets are bringing joy to more and more people every day. ✨</p>

                    <p>With love and support,<br>
                    <strong>Your Website System</strong> 🤖❤️</p>
                </div>

                <div class="footer">
                    <p>This notification was sent automatically when someone subscribed to your newsletter.</p>
                    <p>© ${new Date().getFullYear()} With Love - Julia's Magnets Newsletter System</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate plain text version of newsletter notification email
    generateNewsletterNotificationText(subscriberEmail) {
        const currentDate = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
New Newsletter Subscriber - Julia's Magnets

Hi Julia!

Great news! You have a new subscriber to your newsletter!

Subscriber Details:
Email: ${subscriberEmail}
Subscription Date: ${currentDate}

This person is now part of your growing community and will receive:
• Exclusive design previews
• Special subscriber discounts
• Personalization tips and ideas
• Behind-the-scenes stories
• Seasonal collections updates
• Gift ideas and inspiration

They've already received a beautiful welcome email introducing them to your brand!

Keep up the amazing work! Your handmade magnets are bringing joy to more and more people every day.

With love and support,
Your Website System

---
This notification was sent automatically when someone subscribed to your newsletter.
© ${new Date().getFullYear()} With Love - Julia's Magnets Newsletter System
        `;
    }

    // Generate plain text version of welcome email
    generateWelcomeEmailText(userName) {
        return `
Welcome to With Love - Julia's Magnets!

Dear ${userName},

Welcome to our beautiful community! We're absolutely thrilled to have you join the With Love family.

At With Love, every magnet is crafted with care, love, and attention to detail. You're now part of a special community that appreciates handmade treasures and unique designs.

What you can look forward to:
• Exclusive designs - Unique magnets you won't find anywhere else
• Personalized options - Custom magnets for special occasions  
• Easy ordering - Simple and secure checkout process
• Made with love - Each piece crafted with care and attention
• Special offers - Exclusive deals for our community members

Ready to explore our collection? Visit our shop at: ${process.env.WEBSITE_URL || 'http://localhost:3000'}/Html%20pages/shop.html

If you have any questions or need assistance, don't hesitate to reach out. We're here to help make your experience wonderful!

With love and gratitude,
Julia & The With Love Team

---
This email was sent because you created an account at With Love - Julia's Magnets.
© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with love.
        `;
    }

    // Send mass email to multiple users
    async sendMassEmail(users, subject, message) {


        try {
            if (!users || users.length === 0) {
                return {
                    success: false,
                    error: 'No users provided',
                    totalSent: 0,
                    totalFailed: 0
                };
            }

            console.log(`📧 Starting mass email to ${users.length} users`);
            console.log(`📝 Subject: ${subject}`);

            const results = {
                success: true,
                totalSent: 0,
                totalFailed: 0,
                details: [],
                errors: []
            };

            // Send emails one by one to avoid overwhelming the SMTP server
            for (const user of users) {
                try {
                    const mailOptions = {
                        from: {
                            name: 'With Love - Julia\'s Magnets',
                            address: process.env.EMAIL_FROM || '<EMAIL>'
                        },
                        to: user.email,
                        subject: subject,
                        html: this.generateMassEmailHTML(user.name, subject, message),
                        text: this.generateMassEmailText(user.name, subject, message)
                    };

                    const info = await this.transporter.sendMail(mailOptions);

                    console.log(`✅ Mass email sent successfully to: ${user.email}`);
                    console.log(`📧 Message ID: ${info.messageId}`);

                    results.totalSent++;
                    results.details.push({
                        email: user.email,
                        name: user.name,
                        success: true,
                        messageId: info.messageId
                    });

                    // Small delay to avoid overwhelming the SMTP server
                    await new Promise(resolve => setTimeout(resolve, 100));

                } catch (error) {
                    console.error(`❌ Failed to send mass email to ${user.email}:`, error.message);

                    results.totalFailed++;
                    results.errors.push({
                        email: user.email,
                        name: user.name,
                        error: error.message
                    });
                }
            }

            // Determine overall success
            if (results.totalFailed > 0 && results.totalSent === 0) {
                results.success = false;
                results.error = 'All emails failed to send';
            } else if (results.totalFailed > 0) {
                results.success = true; // Partial success
                results.warning = `${results.totalFailed} emails failed to send`;
            }

            console.log(`📊 Mass email completed: ${results.totalSent} sent, ${results.totalFailed} failed`);

            return results;

        } catch (error) {
            console.error('❌ Mass email error:', error);
            return {
                success: false,
                error: error.message,
                totalSent: 0,
                totalFailed: users ? users.length : 0
            };
        }
    }

    // Generate HTML version of mass email
    generateMassEmailHTML(userName, subject, message) {
        // Convert line breaks to HTML
        const htmlMessage = message.replace(/\n/g, '<br>');

        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${subject}</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }
                .email-container {
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #f0d9d9;
                }
                .logo {
                    font-size: 2em;
                    color: #ff6b8b;
                    margin-bottom: 10px;
                }
                .brand-title {
                    color: #a67f83;
                    font-size: 1.5em;
                    margin: 0;
                }
                .content {
                    margin: 20px 0;
                }
                .highlight {
                    color: #ff6b8b;
                    font-weight: bold;
                }
                .message-content {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    border-left: 4px solid #ff6b8b;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #666;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">❤️</div>
                    <h1 class="brand-title">With Love - Julia's Magnets</h1>
                </div>

                <div class="content">
                    <p>Dear <span class="highlight">${userName}</span>,</p>

                    <div class="message-content">
                        <p>${htmlMessage}</p>
                    </div>

                    <p>Thank you for being part of our wonderful community!</p>

                    <p>With love,<br>
                    <strong>Julia & The With Love Team</strong> ❤️</p>
                </div>

                <div class="footer">
                    <p>You received this email because you're a valued member of our community.</p>
                    <p>© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with ❤️</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // Generate plain text version of mass email
    generateMassEmailText(userName, subject, message) {
        return `
${subject}

Dear ${userName},

${message}

Thank you for being part of our wonderful community!

With love,
Julia & The With Love Team

---
You received this email because you're a valued member of our community.
© ${new Date().getFullYear()} With Love - Julia's Magnets. Made with love.
        `;
    }

    // Test email configuration
    async testEmailConfiguration() {
        try {
            if (!this.transporter) {
                console.log('⚠️  Email transporter not initialized yet');
                return false;
            }

            console.log('🔍 Testing email configuration...');
            await this.transporter.verify();
            console.log('✅ Email configuration is valid');
            return true;
        } catch (error) {
            console.error('❌ Email configuration error:', error.message);

            // If Gmail SMTP fails, suggest fallback
            if (process.env.EMAIL_SERVICE === 'gmail' && (
                error.code === 'ENOTFOUND' ||
                error.message.includes('getaddrinfo') ||
                error.message.includes('smtp.gmail.com')
            )) {
                console.log('💡 Gmail SMTP connection failed. This could be due to:');
                console.log('   1. Network connectivity issues');
                console.log('   2. Firewall blocking SMTP connections');
                console.log('   3. DNS resolution problems');
                console.log('   4. Gmail App Password issues');
                console.log('🔄 Falling back to test email service...');

                try {
                    await this.initializeTestTransporter();
                    console.log('✅ Test email service initialized as fallback');
                    return true;
                } catch (fallbackError) {
                    console.error('❌ Fallback email service also failed:', fallbackError.message);
                }
            }

            return false;
        }
    }

    // Send order confirmation email
    async sendOrderConfirmation(email, customerName, orderNumber, orderData, userInfo = null) {
        if (!this.transporter) {
            throw new Error('Email service not configured');
        }

        // Prepare attachments from cart images
        const attachments = [];
        let attachmentIndex = 1;

        const orderItems = orderData.cart.map(item => {
            let imageAttachmentHtml = '';

            // Check if item has an image and add it as attachment
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Extract base64 data and mime type
                    const matches = item.image.match(/^data:([^;]+);base64,(.+)$/);
                    if (matches) {
                        const mimeType = matches[1];
                        const base64Data = matches[2];
                        const buffer = Buffer.from(base64Data, 'base64');

                        // Create unique filename for this attachment
                        const fileExtension = mimeType.split('/')[1] || 'png';
                        const fileName = `order-${orderNumber}-item-${attachmentIndex}.${fileExtension}`;

                        attachments.push({
                            filename: fileName,
                            content: buffer,
                            contentType: mimeType,
                            cid: `image${attachmentIndex}` // Content ID for inline images
                        });

                        // Reference the attached image in HTML
                        imageAttachmentHtml = `<br><small>📎 Custom image attached: ${fileName}</small>`;
                        attachmentIndex++;
                    }
                } catch (error) {
                    console.warn('Failed to process image attachment for order confirmation:', error);
                }
            }

            let imageDisplayHtml = '';
            let attachmentCid = '';

            // Check if item has an image and create inline display
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Find the corresponding attachment CID
                    const attachmentMatch = attachments.find(att => att.filename.includes(`item-${attachmentIndex - 1}`));
                    if (attachmentMatch) {
                        attachmentCid = attachmentMatch.cid;
                        imageDisplayHtml = `
                            <div style="margin: 10px 0;">
                                <img src="cid:${attachmentCid}" alt="Customer Upload" style="max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #ddd; object-fit: cover;">
                            </div>`;
                    }
                } catch (error) {
                    console.warn('Failed to create inline image display:', error);
                }
            }

            return `
            <tr>
                <td style="padding: 15px; border-bottom: 1px solid #eee;">
                    <div style="display: flex; align-items: flex-start; gap: 15px;">
                        <div style="flex-shrink: 0;">
                            ${imageDisplayHtml || `<div style="width: 80px; height: 80px; background-color: ${item.magnetColor}; border-radius: 8px; border: 2px solid #ddd; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">No Image</div>`}
                        </div>
                        <div style="flex-grow: 1;">
                            <strong style="font-size: 16px; color: #333;">${item.name || 'Custom Magnet'}</strong><br>
                            <div style="margin-top: 8px; font-size: 14px; color: #666;">
                                <div style="margin-bottom: 4px;">
                                    <strong>Magnet Color:</strong>
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.magnetColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                    <span style="margin-left: 8px;">${item.magnetColor}</span>
                                </div>
                                <div style="margin-bottom: 4px;">
                                    <strong>Text Color:</strong>
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.textColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                    <span style="margin-left: 8px;">${item.textColor}</span>
                                </div>
                                <div style="margin-bottom: 4px;"><strong>Quantity:</strong> ${item.quantity}</div>
                                ${item.text ? `<div style="margin-top: 8px; padding: 8px; background-color: #f8f9fa; border-radius: 4px; border-left: 3px solid #007bff;"><strong>Custom Text:</strong> "${item.text}"</div>` : ''}
                                ${imageAttachmentHtml ? `<div style="margin-top: 8px; color: #666; font-size: 12px;">${imageAttachmentHtml}</div>` : ''}
                            </div>
                        </div>
                    </div>
                </td>
                <td style="padding: 15px; border-bottom: 1px solid #eee; text-align: right; vertical-align: top;">
                    <strong style="font-size: 16px; color: #007bff;">$${parseFloat(item.total).toFixed(2)}</strong>
                </td>
            </tr>
        `;
        }).join('');

        const emailHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Order Confirmation</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #ff6b8b, #ff8fab); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">Order Confirmed! 🎉</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px;">Thank you for your order, ${customerName}!</p>
                </div>

                <div style="background: white; padding: 30px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 10px 10px;">
                    <h2 style="color: #ff6b8b; margin-top: 0;">Order Details</h2>
                    <p><strong>Order Number:</strong> ${orderNumber}</p>
                    <p><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>

                    ${userInfo ? `
                    <div style="background: #f0f8ff; border: 1px solid #007bff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                        <h3 style="color: #007bff; margin-top: 0; margin-bottom: 10px;">👤 Account Information</h3>
                        <p style="margin: 5px 0;"><strong>Account Name:</strong> ${userInfo.name}</p>
                        <p style="margin: 5px 0;"><strong>Account Email:</strong> ${userInfo.email}</p>
                        <p style="margin: 5px 0; font-size: 14px; color: #666;">This order was placed using your registered account.</p>
                    </div>
                    ` : ''}

                    <h3 style="color: #333; margin-top: 30px;">Items Ordered:</h3>
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 15px; text-align: left; border-bottom: 2px solid #ff6b8b;">Item</th>
                                <th style="padding: 15px; text-align: right; border-bottom: 2px solid #ff6b8b;">Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${orderItems}
                            <tr style="background: #f8f9fa; font-weight: bold;">
                                <td style="padding: 15px; border-top: 2px solid #ff6b8b;">Shipping</td>
                                <td style="padding: 15px; text-align: right; border-top: 2px solid #ff6b8b; color: #28a745; font-weight: bold;">FREE</td>
                            </tr>
                            <tr style="background: #ff6b8b; color: white; font-weight: bold; font-size: 18px;">
                                <td style="padding: 15px;">Total</td>
                                <td style="padding: 15px; text-align: right;">$${orderData.totals.total.toFixed(2)}</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3 style="color: #333; margin-top: 30px;">Shipping Address:</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <p style="margin: 5px 0;"><strong>${orderData.customerInfo.firstName} ${orderData.customerInfo.lastName}</strong></p>
                        <p style="margin: 5px 0;">${orderData.customerInfo.address}</p>
                        <p style="margin: 5px 0;">${orderData.customerInfo.city}, ${orderData.customerInfo.postal}</p>
                        <p style="margin: 5px 0;">${orderData.customerInfo.country}</p>
                    </div>

                    <h3 style="color: #333; margin-top: 30px;">What's Next?</h3>
                    <ul style="padding-left: 20px;">
                        <li>Your custom magnets will be prepared within 2-3 business days</li>
                        <li>Your order will be shipped within 3-5 business days</li>
                        <li>You'll receive tracking information once shipped</li>
                        <li>Estimated delivery: 7-10 business days</li>
                    </ul>

                    <div style="background: #e8f5e8; border: 1px solid #4caf50; border-radius: 5px; padding: 15px; margin: 20px 0;">
                        <p style="margin: 0; color: #2e7d32;"><strong>💳 Payment Confirmed</strong></p>
                        <p style="margin: 5px 0 0 0; color: #2e7d32;">Your payment has been successfully processed and your order is confirmed.</p>
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <p>Questions about your order? Contact us at <a href="mailto:${process.env.EMAIL_FROM}" style="color: #ff6b8b;">${process.env.EMAIL_FROM}</a></p>
                        <p style="color: #666; font-size: 14px;">Thank you for choosing With Love Handmade Gifts! ❤️</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        const mailOptions = {
            from: `"With Love Handmade Gifts" <${process.env.EMAIL_FROM}>`,
            to: email,
            subject: `Order Confirmation - ${orderNumber}`,
            html: emailHtml,
            attachments: attachments // Include image attachments
        };

        try {
            const result = await this.transporter.sendMail(mailOptions);
            console.log(`✅ Order confirmation email sent to ${email}`);
            return result;
        } catch (error) {
            console.error('❌ Failed to send order confirmation email:', error);
            throw error;
        }
    }

    // Send checkout notification to business owner (before payment)
    async sendCheckoutNotification(orderNumber, orderData, totalAmount, userInfo = null) {
        if (!this.transporter) {
            throw new Error('Email service not configured');
        }

        const ownerEmail = '<EMAIL>';

        // Prepare attachments from cart images
        const attachments = [];
        let attachmentIndex = 1;

        const orderItems = orderData.cart.map(item => {
            const languageText = item.language === 'en' ? 'English' : 'Maltese';
            const hasImage = item.image && item.image.length > 0;
            let imageAttachmentHtml = '';

            // Check if item has an image and add it as attachment
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Extract base64 data and mime type
                    const matches = item.image.match(/^data:([^;]+);base64,(.+)$/);
                    if (matches) {
                        const mimeType = matches[1];
                        const base64Data = matches[2];
                        const buffer = Buffer.from(base64Data, 'base64');

                        // Create unique filename for this attachment
                        const fileExtension = mimeType.split('/')[1] || 'png';
                        const fileName = `checkout-item-${attachmentIndex}.${fileExtension}`;

                        attachments.push({
                            filename: fileName,
                            content: buffer,
                            contentType: mimeType,
                            cid: `checkoutimage${attachmentIndex}` // Content ID for inline images
                        });

                        // Reference the attached image in HTML
                        imageAttachmentHtml = `<br><small>📎 Custom image attached: ${fileName}</small>`;
                        attachmentIndex++;
                    }
                } catch (error) {
                    console.warn('Failed to process image attachment for checkout notification:', error);
                }
            }

            return `
                <tr>
                    <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: top;">
                        <div style="display: flex; align-items: flex-start; gap: 15px;">
                            ${(() => {
                                if (hasImage) {
                                    // Find the corresponding attachment CID for inline display
                                    const attachmentMatch = attachments.find(att => att.filename.includes(`checkout-item-${attachmentIndex - 1}`));
                                    if (attachmentMatch) {
                                        return `<div style="flex-shrink: 0;">
                                            <img src="cid:${attachmentMatch.cid}" alt="Customer Upload" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; border: 2px solid #ddd;">
                                        </div>`;
                                    }
                                }
                                return `<div style="flex-shrink: 0; width: 80px; height: 80px; background-color: ${item.magnetColor}; border-radius: 8px; border: 2px solid #ddd; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">
                                    No Image
                                </div>`;
                            })()}
                            <div style="flex-grow: 1;">
                                <strong style="font-size: 16px; color: #333;">${item.name || 'Custom Magnet'}</strong><br>
                                <div style="margin-top: 8px; font-size: 14px; color: #666;">
                                    <div style="margin-bottom: 4px;">
                                        <strong>Magnet Color:</strong>
                                        <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.magnetColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                        <span style="margin-left: 8px;">${item.magnetColor}</span>
                                    </div>
                                    <div style="margin-bottom: 4px;">
                                        <strong>Text Color:</strong>
                                        <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.textColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                        <span style="margin-left: 8px;">${item.textColor}</span>
                                    </div>
                                    <div style="margin-bottom: 4px;"><strong>Language:</strong> ${languageText}</div>
                                    <div><strong>Quantity:</strong> ${item.quantity}</div>
                                    ${item.text ? `<div style="margin-top: 8px; padding: 8px; background-color: #f8f9fa; border-radius: 4px; border-left: 3px solid #ff6b8b;"><strong>Custom Text:</strong> "${item.text}"</div>` : ''}
                                    ${imageAttachmentHtml ? `<div style="margin-top: 8px; color: #666; font-size: 12px;">${imageAttachmentHtml}</div>` : ''}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #eee; text-align: right; vertical-align: top;">
                        <strong style="font-size: 16px; color: #ff6b8b;">€${parseFloat(item.total).toFixed(2)}</strong>
                    </td>
                </tr>
            `;
        }).join('');

        const emailHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>New Checkout Started</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 700px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #ff6b8b, #ff8fab); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">🛒 New Checkout Started!</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px;">A customer is proceeding to payment</p>
                </div>

                <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
                        <h3 style="margin: 0 0 10px 0; color: #856404;">⚠️ Checkout Notification</h3>
                        <p style="margin: 0; color: #856404;">This email is sent when a customer clicks "Proceed to Checkout". The actual payment may or may not be completed.</p>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #ff6b8b; border-bottom: 2px solid #ff6b8b; padding-bottom: 10px;">Order Details</h3>
                        <p><strong>Reference:</strong> ${orderNumber}</p>
                        <p><strong>Date:</strong> ${new Date(orderData.timestamp).toLocaleString()}</p>
                        <p><strong>Total Amount:</strong> <span style="font-size: 18px; color: #ff6b8b; font-weight: bold;">€${totalAmount.toFixed(2)}</span></p>
                    </div>

                    ${userInfo ? `
                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
                        <h3 style="color: #28a745; margin-top: 0; margin-bottom: 10px;">👤 Registered User</h3>
                        <p style="margin: 5px 0;"><strong>Account Name:</strong> ${userInfo.name}</p>
                        <p style="margin: 5px 0;"><strong>Account Email:</strong> ${userInfo.email}</p>
                        <p style="margin: 5px 0; font-size: 14px; color: #666;">This customer is logged in with a registered account.</p>
                    </div>
                    ` : `
                    <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
                        <h3 style="color: #856404; margin-top: 0; margin-bottom: 10px;">👤 Guest Customer</h3>
                        <p style="margin: 5px 0; font-size: 14px; color: #856404;">This customer is not logged in (guest checkout).</p>
                    </div>
                    `}

                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #ff6b8b; border-bottom: 2px solid #ff6b8b; padding-bottom: 10px;">Items</h3>
                        <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="padding: 15px; text-align: left; border-bottom: 2px solid #dee2e6;">Product Details</th>
                                    <th style="padding: 15px; text-align: right; border-bottom: 2px solid #dee2e6;">Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${orderItems}
                            </tbody>
                            <tfoot>
                                <tr style="background-color: #f8f9fa;">
                                    <td style="padding: 15px; text-align: right; border-top: 2px solid #dee2e6;"><strong>Total:</strong></td>
                                    <td style="padding: 15px; text-align: right; border-top: 2px solid #dee2e6;"><strong style="font-size: 18px; color: #ff6b8b;">€${totalAmount.toFixed(2)}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <p style="color: #666; font-size: 14px;">This is an automated notification from With Love Handmade Gifts checkout system.</p>
                        <p style="color: #666; font-size: 14px;">You will receive another email if the payment is successfully completed.</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        const mailOptions = {
            from: `"With Love Handmade Gifts - Checkout System" <${process.env.EMAIL_FROM}>`,
            to: ownerEmail,
            subject: `🛒 New Checkout Started - €${totalAmount.toFixed(2)} (${orderData.cart.length} item${orderData.cart.length > 1 ? 's' : ''})`,
            html: emailHtml,
            attachments: attachments // Include image attachments
        };

        try {
            const result = await this.transporter.sendMail(mailOptions);
            console.log(`✅ Checkout notification email sent to ${ownerEmail}`);
            return result;
        } catch (error) {
            console.error('❌ Failed to send checkout notification email:', error);
            throw error;
        }
    }

    // Send order notification to business owner
    async sendOwnerNotification(orderNumber, orderData, paymentAmount, userInfo = null) {
        if (!this.transporter) {
            throw new Error('Email service not configured');
        }

        const ownerEmail = '<EMAIL>';
        const customerInfo = orderData.customerInfo;

        // Prepare attachments from cart images
        const attachments = [];
        let attachmentIndex = 1;

        const orderItems = orderData.cart.map(item => {
            let imageAttachmentHtml = '';

            // Check if item has an image and add it as attachment
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Extract base64 data and mime type
                    const matches = item.image.match(/^data:([^;]+);base64,(.+)$/);
                    if (matches) {
                        const mimeType = matches[1];
                        const base64Data = matches[2];
                        const buffer = Buffer.from(base64Data, 'base64');

                        // Create unique filename for this attachment
                        const fileExtension = mimeType.split('/')[1] || 'png';
                        const fileName = `order-${orderNumber}-item-${attachmentIndex}.${fileExtension}`;

                        attachments.push({
                            filename: fileName,
                            content: buffer,
                            contentType: mimeType,
                            cid: `ownerimage${attachmentIndex}` // Content ID for inline images
                        });

                        // Reference the attached image in HTML
                        imageAttachmentHtml = `<br><small>📎 Custom image attached: ${fileName}</small>`;
                        attachmentIndex++;
                    }
                } catch (error) {
                    console.warn('Failed to process image attachment for owner notification:', error);
                }
            }

            let imageDisplayHtml = '';
            let attachmentCid = '';

            // Check if item has an image and create inline display
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Find the corresponding attachment CID
                    const attachmentMatch = attachments.find(att => att.filename.includes(`item-${attachmentIndex - 1}`));
                    if (attachmentMatch) {
                        attachmentCid = attachmentMatch.cid;
                        imageDisplayHtml = `
                            <div style="margin: 10px 0;">
                                <img src="cid:${attachmentCid}" alt="Customer Upload" style="max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #ddd; object-fit: cover;">
                            </div>`;
                    }
                } catch (error) {
                    console.warn('Failed to create inline image display for owner notification:', error);
                }
            }

            return `
            <tr>
                <td style="padding: 15px; border-bottom: 1px solid #eee;">
                    <div style="display: flex; align-items: flex-start; gap: 15px;">
                        <div style="flex-shrink: 0;">
                            ${imageDisplayHtml || `<div style="width: 80px; height: 80px; background-color: ${item.magnetColor}; border-radius: 8px; border: 2px solid #ddd; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">No Image</div>`}
                        </div>
                        <div style="flex-grow: 1;">
                            <strong style="font-size: 16px; color: #333;">${item.name || 'Custom Magnet'}</strong><br>
                            <div style="margin-top: 8px; font-size: 14px; color: #666;">
                                <div style="margin-bottom: 4px;">
                                    <strong>Magnet Color:</strong>
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.magnetColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                    <span style="margin-left: 8px;">${item.magnetColor}</span>
                                </div>
                                <div style="margin-bottom: 4px;">
                                    <strong>Text Color:</strong>
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: ${item.textColor}; border: 1px solid #ddd; border-radius: 50%; margin-left: 8px; vertical-align: middle;"></span>
                                    <span style="margin-left: 8px;">${item.textColor}</span>
                                </div>
                                <div style="margin-bottom: 4px;"><strong>Quantity:</strong> ${item.quantity}</div>
                                ${item.text ? `<div style="margin-top: 8px; padding: 8px; background-color: #f8f9fa; border-radius: 4px; border-left: 3px solid #ff6b8b;"><strong>Custom Text:</strong> "${item.text}"</div>` : ''}
                                ${imageAttachmentHtml ? `<div style="margin-top: 8px; color: #666; font-size: 12px;">${imageAttachmentHtml}</div>` : ''}
                            </div>
                        </div>
                    </div>
                </td>
                <td style="padding: 15px; border-bottom: 1px solid #eee; text-align: right; vertical-align: top;">
                    <strong style="font-size: 16px; color: #ff6b8b;">$${parseFloat(item.total).toFixed(2)}</strong>
                </td>
            </tr>
        `;
        }).join('');

        const emailHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>New Order Received!</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">🎉 New Order Received!</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px;">You have a new magnet order!</p>
                </div>

                <div style="background: white; padding: 30px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 10px 10px;">
                    <h2 style="color: #28a745; margin-top: 0;">Order Details</h2>
                    <p><strong>Order Number:</strong> ${orderNumber}</p>
                    <p><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>
                    <p><strong>Payment Amount:</strong> <span style="color: #28a745; font-size: 18px; font-weight: bold;">$${paymentAmount.toFixed(2)}</span></p>

                    <h3 style="color: #333; margin-top: 30px;">Customer Information:</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <p style="margin: 5px 0;"><strong>Name:</strong> ${customerInfo.firstName} ${customerInfo.lastName}</p>
                        <p style="margin: 5px 0;"><strong>Email:</strong> ${customerInfo.email}</p>
                        <p style="margin: 5px 0;"><strong>Phone:</strong> ${customerInfo.phone}</p>
                        <p style="margin: 5px 0;"><strong>Address:</strong> ${customerInfo.address}</p>
                        <p style="margin: 5px 0;"><strong>City:</strong> ${customerInfo.city}, ${customerInfo.postal}</p>
                        <p style="margin: 5px 0;"><strong>Country:</strong> ${customerInfo.country}</p>
                    </div>

                    ${userInfo ? `
                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin: 20px 0;">
                        <h4 style="color: #28a745; margin-top: 0; margin-bottom: 10px;">👤 Registered User Account</h4>
                        <p style="margin: 5px 0;"><strong>Account Name:</strong> ${userInfo.name}</p>
                        <p style="margin: 5px 0;"><strong>Account Email:</strong> ${userInfo.email}</p>
                        <p style="margin: 5px 0; font-size: 14px; color: #666;">This customer has a registered account on your website.</p>
                    </div>
                    ` : `
                    <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 15px; margin: 20px 0;">
                        <h4 style="color: #856404; margin-top: 0; margin-bottom: 10px;">👤 Guest Customer</h4>
                        <p style="margin: 5px 0; font-size: 14px; color: #856404;">This order was placed by a guest customer (not logged in).</p>
                    </div>
                    `}

                    <h3 style="color: #333; margin-top: 30px;">Items Ordered:</h3>
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 15px; text-align: left; border-bottom: 2px solid #28a745;">Item Details</th>
                                <th style="padding: 15px; text-align: right; border-bottom: 2px solid #28a745;">Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${orderItems}
                            <tr style="background: #f8f9fa; font-weight: bold;">
                                <td style="padding: 15px; border-top: 2px solid #28a745;">Shipping</td>
                                <td style="padding: 15px; text-align: right; border-top: 2px solid #28a745; color: #28a745; font-weight: bold;">FREE</td>
                            </tr>
                            <tr style="background: #28a745; color: white; font-weight: bold; font-size: 18px;">
                                <td style="padding: 15px;">Total Paid</td>
                                <td style="padding: 15px; text-align: right;">$${orderData.totals.total.toFixed(2)}</td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 5px; padding: 15px; margin: 20px 0;">
                        <p style="margin: 0; color: #2e7d32;"><strong>💳 Payment Status: PAID</strong></p>
                        <p style="margin: 5px 0 0 0; color: #2e7d32;">Payment has been successfully processed through Stripe.</p>
                    </div>

                    <h3 style="color: #333; margin-top: 30px;">Next Steps:</h3>
                    <ul style="padding-left: 20px;">
                        <li>Prepare the custom magnets according to specifications</li>
                        <li>Process the order within 2-3 business days</li>
                        <li>Ship the order within 3-5 business days</li>
                        <li>Send tracking information to the customer</li>
                    </ul>

                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <p style="color: #666; font-size: 14px;">This is an automated notification from your magnet website.</p>
                        <p style="color: #666; font-size: 14px;">Order received at ${new Date().toLocaleString()}</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        const mailOptions = {
            from: `"With Love Handmade Gifts - Order System" <${process.env.EMAIL_FROM}>`,
            to: ownerEmail,
            subject: `🎉 New Order #${orderNumber} - $${paymentAmount.toFixed(2)}`,
            html: emailHtml,
            attachments: attachments // Include image attachments
        };

        try {
            const result = await this.transporter.sendMail(mailOptions);
            console.log(`✅ Owner notification email sent to ${ownerEmail}`);
            return result;
        } catch (error) {
            console.error('❌ Failed to send owner notification email:', error);
            throw error;
        }
    }

    // Send test image email
    async sendTestImageEmail(imageBuffer, fileName, retryCount = 0) {
        const maxRetries = 3;

        if (!this.transporter) {
            throw new Error('Email service not configured');
        }

        try {
            // Test connection before sending
            try {
                await this.transporter.verify();
                console.log('✅ SMTP connection verified for test image email');
            } catch (verifyError) {
                console.error('❌ SMTP verification failed:', verifyError.message);

                // If Gmail SMTP fails, try fallback to test email
                if (process.env.EMAIL_SERVICE === 'gmail' && retryCount === 0) {
                    console.log('🔄 Gmail SMTP failed, trying test email service...');
                    await this.initializeTestTransporter();
                    return this.sendTestImageEmail(imageBuffer, fileName, retryCount + 1);
                }

                throw verifyError;
            }

            const recipientEmail = '<EMAIL>';

            const mailOptions = {
                from: {
                    name: 'With Love - Julia\'s Magnets Test System',
                    address: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: recipientEmail,
                subject: '🧪 Test Image Upload - Julia\'s Magnets',
                html: this.generateTestImageEmailHTML(fileName),
                text: this.generateTestImageEmailText(fileName),
                attachments: [
                    {
                        filename: fileName,
                        content: imageBuffer,
                        contentType: 'image/*'
                    }
                ]
            };

            console.log(`📤 Sending test image email to: ${recipientEmail}`);
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ Test image email sent successfully to:', recipientEmail);
            console.log('📧 Message ID:', info.messageId);

            // If using Ethereal Email, log the preview URL
            if (info.previewURL) {
                console.log('🔗 Preview URL:', nodemailer.getTestMessageUrl(info));
            }

            return {
                success: true,
                messageId: info.messageId,
                previewURL: info.previewURL ? nodemailer.getTestMessageUrl(info) : null
            };

        } catch (error) {
            console.error(`❌ Failed to send test image email (attempt ${retryCount + 1}/${maxRetries + 1}):`, error.message);

            // Retry logic for network errors
            if (retryCount < maxRetries && (
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.message.includes('getaddrinfo')
            )) {
                console.log(`🔄 Retrying in ${(retryCount + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000));
                return this.sendTestImageEmail(imageBuffer, fileName, retryCount + 1);
            }

            return {
                success: false,
                error: error.message,
                code: error.code
            };
        }
    }

    // Generate HTML for test image email
    generateTestImageEmailHTML(fileName) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
                .highlight { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧪 Test Image Upload</h1>
                    <p>Julia's Magnets Testing System</p>
                </div>
                <div class="content">
                    <h2>Test Image Received!</h2>
                    <p>A test image has been successfully uploaded and sent through the testing system.</p>

                    <div class="highlight">
                        <h3>📎 Attachment Details:</h3>
                        <p><strong>File Name:</strong> ${fileName}</p>
                        <p><strong>Upload Time:</strong> ${new Date().toLocaleString()}</p>
                        <p><strong>System:</strong> Julia's Magnets Test Upload</p>
                    </div>

                    <p>This email confirms that the image upload and email functionality is working correctly.</p>

                    <p>The uploaded image is attached to this email.</p>
                </div>
                <div class="footer">
                    <p>This is an automated test email from Julia's Magnets Testing System</p>
                    <p>© ${new Date().getFullYear()} With Love - Julia's Magnets</p>
                </div>
            </div>
        </body>
        </html>`;
    }

    // Generate plain text for test image email
    generateTestImageEmailText(fileName) {
        return `
🧪 Test Image Upload - Julia's Magnets

Test Image Received!

A test image has been successfully uploaded and sent through the testing system.

📎 Attachment Details:
File Name: ${fileName}
Upload Time: ${new Date().toLocaleString()}
System: Julia's Magnets Test Upload

This email confirms that the image upload and email functionality is working correctly.

The uploaded image is attached to this email.

This is an automated test email from Julia's Magnets Testing System
© ${new Date().getFullYear()} With Love - Julia's Magnets`;
    }


}

module.exports = new EmailService();
