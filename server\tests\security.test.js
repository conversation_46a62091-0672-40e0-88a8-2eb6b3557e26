/**
 * Security Tests
 * Tests for security middleware, CSRF protection, rate limiting, and input validation
 */

const request = require('supertest');
const express = require('express');
const session = require('express-session');
const security = require('../middleware/security');

// Create test app
function createTestApp() {
    const app = express();
    
    // Session middleware for CSRF
    app.use(session({
        secret: 'test-secret',
        resave: false,
        saveUninitialized: false,
        cookie: { secure: false }
    }));
    
    app.use(express.json());
    app.use(security.securityHeaders);
    app.use(security.sanitizeInput);
    app.use(security.validateRequest);
    
    // Test routes
    app.get('/api/csrf-token', security.getCSRFToken);
    app.post('/api/test', security.csrfProtection, (req, res) => {
        res.json({ success: true, data: req.body });
    });
    
    app.get('/api/test-headers', (req, res) => {
        res.json({ success: true });
    });
    
    app.post('/api/test-validation', (req, res) => {
        res.json({ success: true, data: req.body });
    });
    
    app.use(security.errorHandler);
    
    return app;
}

describe('Security Middleware Tests', () => {
    let app;
    
    beforeEach(() => {
        app = createTestApp();
    });
    
    describe('Security Headers', () => {
        test('should set security headers', async () => {
            const response = await request(app)
                .get('/api/test-headers')
                .expect(200);
            
            expect(response.headers['x-content-type-options']).toBe('nosniff');
            expect(response.headers['x-frame-options']).toBe('DENY');
            expect(response.headers['x-xss-protection']).toBe('1; mode=block');
            expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
            expect(response.headers['permissions-policy']).toBe('geolocation=(), microphone=(), camera=()');
        });
        
        test('should remove X-Powered-By header', async () => {
            const response = await request(app)
                .get('/api/test-headers')
                .expect(200);
            
            expect(response.headers['x-powered-by']).toBeUndefined();
        });
    });
    
    describe('CSRF Protection', () => {
        test('should provide CSRF token', async () => {
            const response = await request(app)
                .get('/api/csrf-token')
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.csrfToken).toBeDefined();
            expect(typeof response.body.data.csrfToken).toBe('string');
            expect(response.body.data.csrfToken.length).toBe(64); // 32 bytes hex = 64 chars
        });
        
        test('should reject POST without CSRF token', async () => {
            const response = await request(app)
                .post('/api/test')
                .send({ test: 'data' })
                .expect(403);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('CSRF_TOKEN_INVALID');
        });
        
        test('should accept POST with valid CSRF token', async () => {
            // First get CSRF token
            const agent = request.agent(app);
            const tokenResponse = await agent
                .get('/api/csrf-token')
                .expect(200);
            
            const csrfToken = tokenResponse.body.data.csrfToken;
            
            // Then make POST request with token
            const response = await agent
                .post('/api/test')
                .set('X-CSRF-Token', csrfToken)
                .send({ test: 'data' })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.test).toBe('data');
        });
        
        test('should reject POST with invalid CSRF token', async () => {
            const response = await request(app)
                .post('/api/test')
                .set('X-CSRF-Token', 'invalid-token')
                .send({ test: 'data' })
                .expect(403);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('CSRF_TOKEN_INVALID');
        });
    });
    
    describe('Input Validation', () => {
        test('should reject XSS attempts in body', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    name: '<script>alert("xss")</script>',
                    description: 'Normal text'
                })
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('INVALID_INPUT');
        });
        
        test('should reject JavaScript protocol in body', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    url: 'javascript:alert("xss")'
                })
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('INVALID_INPUT');
        });
        
        test('should reject event handlers in body', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    content: '<div onclick="alert(1)">Click me</div>'
                })
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('INVALID_INPUT');
        });
        
        test('should accept clean input', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    name: 'John Doe',
                    email: '<EMAIL>',
                    description: 'This is a normal description with <b>some HTML</b>'
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
        });
    });
    
    describe('Input Sanitization', () => {
        test('should remove null bytes', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    name: 'John\0Doe'
                })
                .expect(200);
            
            expect(response.body.data.name).toBe('JohnDoe');
        });
        
        test('should trim whitespace', async () => {
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    name: '  John Doe  '
                })
                .expect(200);
            
            expect(response.body.data.name).toBe('John Doe');
        });
        
        test('should limit string length', async () => {
            const longString = 'a'.repeat(15000);
            const response = await request(app)
                .post('/api/test-validation')
                .send({ 
                    description: longString
                })
                .expect(200);
            
            expect(response.body.data.description.length).toBe(10000);
        });
    });
    
    describe('Rate Limiting', () => {
        test('should apply rate limiting', async () => {
            const testApp = express();
            testApp.use(express.json());
            testApp.use('/api', security.strictApiLimiter);
            testApp.post('/api/test', (req, res) => {
                res.json({ success: true });
            });
            
            // Make requests up to the limit
            const promises = [];
            for (let i = 0; i < 21; i++) {
                promises.push(
                    request(testApp)
                        .post('/api/test')
                        .send({ test: i })
                );
            }
            
            const responses = await Promise.all(promises);
            
            // First 20 should succeed
            for (let i = 0; i < 20; i++) {
                expect(responses[i].status).toBe(200);
            }
            
            // 21st should be rate limited
            expect(responses[20].status).toBe(429);
            expect(responses[20].body.error.code).toBe('RATE_LIMIT_EXCEEDED');
        });
    });
    
    describe('Error Handling', () => {
        test('should handle errors gracefully', async () => {
            const testApp = express();
            testApp.get('/api/error', (req, res, next) => {
                const error = new Error('Test error');
                error.status = 500;
                error.code = 'TEST_ERROR';
                next(error);
            });
            testApp.use(security.errorHandler);
            
            const response = await request(testApp)
                .get('/api/error')
                .expect(500);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('TEST_ERROR');
        });
    });
});

module.exports = {
    createTestApp
};
