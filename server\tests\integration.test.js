/**
 * Integration Tests
 * End-to-end tests for authentication and booking functionality
 */

const request = require('supertest');
const path = require('path');
const fs = require('fs');

// Import the app
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const session = require('express-session');
const database = require('../config/database');
const authRoutes = require('../routes/auth');
const bookingRoutes = require('../routes/bookings');
const security = require('../middleware/security');

// Create test app
function createTestApp() {
    const app = express();
    
    // Basic middleware
    app.use(helmet({ contentSecurityPolicy: false }));
    app.use(cors({ credentials: true }));
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Session for CSRF
    app.use(session({
        secret: 'test-secret',
        resave: false,
        saveUninitialized: false,
        cookie: { secure: false }
    }));
    
    // Security middleware
    app.use(security.securityHeaders);
    app.use(security.sanitizeInput);
    app.use(security.validateRequest);
    
    // Routes
    app.get('/api/csrf-token', security.getCSRFToken);
    app.use('/api/auth', security.csrfProtection, authRoutes);
    app.use('/api/bookings', security.csrfProtection, bookingRoutes);
    
    app.use(security.errorHandler);
    
    return app;
}

describe('Integration Tests', () => {
    let app;
    let agent;
    let csrfToken;
    let authToken;
    let testUser;
    
    beforeAll(async () => {
        // Connect to test database
        await database.connect();
        app = createTestApp();
        agent = request.agent(app);
        
        // Get CSRF token
        const csrfResponse = await agent.get('/api/csrf-token');
        csrfToken = csrfResponse.body.data.csrfToken;
    });
    
    afterAll(async () => {
        // Clean up test data
        if (testUser) {
            await database.run('DELETE FROM users WHERE email = ?', [testUser.email]);
        }
        await database.run('DELETE FROM bookings WHERE user_id IN (SELECT id FROM users WHERE email LIKE "%test%")');
        await database.close();
    });
    
    describe('Authentication Flow', () => {
        test('should signup a new user', async () => {
            const userData = {
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>',
                password: 'TestPassword123!',
                phone: '+356 1234 5678'
            };
            
            const response = await agent
                .post('/api/auth/signup')
                .set('X-CSRF-Token', csrfToken)
                .send(userData)
                .expect(201);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(userData.email);
            expect(response.body.data.user.firstName).toBe(userData.firstName);
            expect(response.body.data.token).toBeDefined();
            
            testUser = response.body.data.user;
            authToken = response.body.data.token;
        });
        
        test('should not signup user with existing email', async () => {
            const userData = {
                firstName: 'Another',
                lastName: 'User',
                email: '<EMAIL>', // Same email
                password: 'AnotherPassword123!',
                phone: '+356 8765 4321'
            };
            
            const response = await agent
                .post('/api/auth/signup')
                .set('X-CSRF-Token', csrfToken)
                .send(userData)
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('EMAIL_EXISTS');
        });
        
        test('should login with correct credentials', async () => {
            const response = await agent
                .post('/api/auth/login')
                .set('X-CSRF-Token', csrfToken)
                .send({
                    email: '<EMAIL>',
                    password: 'TestPassword123!'
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe('<EMAIL>');
            expect(response.body.data.token).toBeDefined();
            
            authToken = response.body.data.token;
        });
        
        test('should not login with incorrect password', async () => {
            const response = await agent
                .post('/api/auth/login')
                .set('X-CSRF-Token', csrfToken)
                .send({
                    email: '<EMAIL>',
                    password: 'WrongPassword'
                })
                .expect(401);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
        });
        
        test('should verify valid token', async () => {
            const response = await agent
                .get('/api/auth/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe('<EMAIL>');
        });
        
        test('should get user profile', async () => {
            const response = await agent
                .get('/api/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe('<EMAIL>');
            expect(response.body.data.user.firstName).toBe('Test');
        });
    });
    
    describe('Booking Flow', () => {
        let facilityId;
        let bookingId;
        
        test('should get available facilities', async () => {
            const response = await agent
                .get('/api/bookings/facilities')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.facilities).toBeDefined();
            expect(Array.isArray(response.body.data.facilities)).toBe(true);
            expect(response.body.data.facilities.length).toBeGreaterThan(0);
            
            facilityId = response.body.data.facilities[0].id;
        });
        
        test('should check facility availability', async () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateStr = tomorrow.toISOString().split('T')[0];
            
            const response = await agent
                .get(`/api/bookings/availability/${facilityId}?date=${dateStr}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.availableSlots).toBeDefined();
            expect(Array.isArray(response.body.data.availableSlots)).toBe(true);
        });
        
        test('should create a booking', async () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateStr = tomorrow.toISOString().split('T')[0];
            
            const bookingData = {
                facilityId: facilityId,
                date: dateStr,
                time: '10:00',
                duration: 'full',
                notes: 'Test booking'
            };
            
            const response = await agent
                .post('/api/bookings')
                .set('Authorization', `Bearer ${authToken}`)
                .set('X-CSRF-Token', csrfToken)
                .send(bookingData)
                .expect(201);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.booking.facility_id).toBe(facilityId);
            expect(response.body.data.booking.booking_date).toBe(dateStr);
            expect(response.body.data.booking.booking_time).toBe('10:00');
            expect(response.body.data.booking.status).toBe('confirmed');
            
            bookingId = response.body.data.booking.id;
        });
        
        test('should get user bookings', async () => {
            const response = await agent
                .get('/api/bookings')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.bookings).toBeDefined();
            expect(Array.isArray(response.body.data.bookings)).toBe(true);
            expect(response.body.data.bookings.length).toBeGreaterThan(0);
            
            const booking = response.body.data.bookings.find(b => b.id === bookingId);
            expect(booking).toBeDefined();
            expect(booking.status).toBe('confirmed');
        });
        
        test('should get specific booking', async () => {
            const response = await agent
                .get(`/api/bookings/${bookingId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.booking.id).toBe(bookingId);
            expect(response.body.data.booking.status).toBe('confirmed');
        });
        
        test('should cancel booking', async () => {
            const response = await agent
                .put(`/api/bookings/${bookingId}/cancel`)
                .set('Authorization', `Bearer ${authToken}`)
                .set('X-CSRF-Token', csrfToken)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.booking.status).toBe('cancelled');
        });
        
        test('should not create booking without authentication', async () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const dateStr = tomorrow.toISOString().split('T')[0];
            
            const bookingData = {
                facilityId: facilityId,
                date: dateStr,
                time: '14:00',
                duration: 'full'
            };
            
            const response = await agent
                .post('/api/bookings')
                .set('X-CSRF-Token', csrfToken)
                .send(bookingData)
                .expect(401);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('NO_TOKEN');
        });
    });
    
    describe('Error Handling', () => {
        test('should handle invalid booking data', async () => {
            const response = await agent
                .post('/api/bookings')
                .set('Authorization', `Bearer ${authToken}`)
                .set('X-CSRF-Token', csrfToken)
                .send({
                    facilityId: 'invalid',
                    date: 'invalid-date',
                    time: 'invalid-time'
                })
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('VALIDATION_ERROR');
        });
        
        test('should handle non-existent booking', async () => {
            const response = await agent
                .get('/api/bookings/99999')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('BOOKING_NOT_FOUND');
        });
    });
});
