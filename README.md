# Sports Booking Platform

A comprehensive multi-sport facility booking platform for Malta, allowing users to reserve football pitches, tennis courts, and padel courts across various venues.

## Features

- **Multi-Sport Booking**: Football, Tennis, and Padel facilities
- **Real-Time Availability**: Live booking updates and conflict prevention
- **Responsive Design**: Mobile-first approach with touch-friendly interface
- **User Management**: Registration, authentication, and booking history
- **Payment Integration**: Secure payment processing with Stripe
- **Venue Management**: Multiple venues and facilities across Malta
- **Email Notifications**: Booking confirmations and reminders
- **Newsletter System**: Updates and promotional offers

## Technology Stack

### Frontend
- HTML5, CSS3, JavaScript (ES6+)
- Responsive design with mobile-first approach
- Progressive Web App features

### Backend
- Node.js with Express.js
- PostgreSQL database
- Redis for caching and sessions
- WebSocket for real-time updates
- JWT authentication

### External Services
- Stripe for payment processing
- SMTP for email notifications
- Image storage and optimization

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- Redis (v6 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sports-booking-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create database
   createdb sports_booking
   
   # Run migrations
   psql -d sports_booking -f database/migrations/001_initial_schema.sql
   
   # Seed sample data (optional)
   psql -d sports_booking -f database/seeds/001_sample_data.sql
   ```

5. **Start Redis server**
   ```bash
   redis-server
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## Project Structure

```
sports-booking-platform/
├── public/                 # Frontend static files
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Image assets
│   └── *.html             # HTML pages
├── server/                # Backend application
│   ├── config/            # Configuration files
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Custom middleware
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   └── utils/             # Utility functions
├── database/              # Database files
│   ├── migrations/        # Database migrations
│   └── seeds/             # Sample data
├── .kiro/                 # Kiro specifications
│   └── specs/             # Feature specifications
└── tests/                 # Test files
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Venues & Facilities
- `GET /api/venues` - List all venues
- `GET /api/venues/:id` - Get venue details
- `GET /api/facilities` - List facilities (with filters)
- `GET /api/facilities/:id` - Get facility details

### Bookings
- `GET /api/bookings` - User's bookings
- `POST /api/bookings` - Create new booking
- `GET /api/bookings/:id` - Get booking details
- `PUT /api/bookings/:id` - Update booking
- `DELETE /api/bookings/:id` - Cancel booking

### Availability
- `GET /api/availability/:facilityId` - Get facility availability
- `POST /api/availability/check` - Check specific time slot

### Payments
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/confirm` - Confirm payment
- `POST /api/payments/webhook` - Stripe webhook

## Development

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --testPathPattern=booking
```

### Code Style
The project uses ESLint and Prettier for code formatting. Run:
```bash
npm run lint
npm run format
```

### Database Migrations
To create a new migration:
```bash
# Create migration file
touch database/migrations/002_new_feature.sql

# Apply migration
psql -d sports_booking -f database/migrations/002_new_feature.sql
```

## Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
Ensure all production environment variables are set:
- Database connection details
- Redis configuration
- JWT secret
- Stripe keys
- SMTP settings

### Docker (Optional)
```bash
# Build image
docker build -t sports-booking-platform .

# Run container
docker run -p 5000:5000 sports-booking-platform
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Phone: +356 1234 5678

## Roadmap

- [ ] Mobile app development
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Integration with more payment providers
- [ ] Loyalty program implementation
- [ ] Advanced booking rules and restrictions