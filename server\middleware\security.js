/**
 * Security Middleware
 * Provides additional security measures including CSRF protection,
 * request validation, and security headers
 */

const crypto = require('crypto');
const rateLimit = require('express-rate-limit');

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    return crypto.randomBytes(32).toString('hex');
}

/**
 * CSRF Protection Middleware
 */
const csrfProtection = (req, res, next) => {
    // Skip CSRF for GET requests and API health checks
    if (req.method === 'GET' || req.path === '/api/health') {
        return next();
    }

    // Generate CSRF token for session if not exists
    if (!req.session.csrfToken) {
        req.session.csrfToken = generateCSRFToken();
    }

    // For POST/PUT/DELETE requests, verify CSRF token
    if (['POST', 'PUT', 'DELETE'].includes(req.method)) {
        const token = req.headers['x-csrf-token'] || req.body._csrf;
        
        if (!token || token !== req.session.csrfToken) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'CSRF_TOKEN_INVALID',
                    message: 'Invalid CSRF token'
                }
            });
        }
    }

    next();
};

/**
 * Get CSRF token endpoint
 */
const getCSRFToken = (req, res) => {
    if (!req.session.csrfToken) {
        req.session.csrfToken = generateCSRFToken();
    }
    
    res.json({
        success: true,
        data: { csrfToken: req.session.csrfToken }
    });
};

/**
 * Request validation middleware
 */
const validateRequest = (req, res, next) => {
    // Check for suspicious patterns in request
    const suspiciousPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /eval\s*\(/gi,
        /expression\s*\(/gi
    ];

    const checkString = (str) => {
        return suspiciousPatterns.some(pattern => pattern.test(str));
    };

    const checkObject = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string' && checkString(obj[key])) {
                return true;
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                if (checkObject(obj[key])) return true;
            }
        }
        return false;
    };

    // Check request body
    if (req.body && checkObject(req.body)) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'INVALID_INPUT',
                message: 'Request contains invalid content'
            }
        });
    }

    // Check query parameters
    if (req.query && checkObject(req.query)) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'INVALID_INPUT',
                message: 'Request contains invalid content'
            }
        });
    }

    next();
};

/**
 * API Rate limiting
 */
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests, please try again later'
        }
    },
    standardHeaders: true,
    legacyHeaders: false
});

/**
 * Strict API Rate limiting for sensitive endpoints
 */
const strictApiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 20, // limit each IP to 20 requests per windowMs
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests, please try again later'
        }
    },
    standardHeaders: true,
    legacyHeaders: false
});

/**
 * Security headers middleware
 */
const securityHeaders = (req, res, next) => {
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    // HSTS header for HTTPS
    if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
    
    next();
};

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
    const sanitizeString = (str) => {
        if (typeof str !== 'string') return str;
        
        // Remove null bytes
        str = str.replace(/\0/g, '');
        
        // Trim whitespace
        str = str.trim();
        
        // Limit string length
        if (str.length > 10000) {
            str = str.substring(0, 10000);
        }
        
        return str;
    };

    const sanitizeObject = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                obj[key] = sanitizeString(obj[key]);
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitizeObject(obj[key]);
            }
        }
    };

    if (req.body) sanitizeObject(req.body);
    if (req.query) sanitizeObject(req.query);
    if (req.params) sanitizeObject(req.params);

    next();
};

/**
 * Error handling middleware
 */
const errorHandler = (err, req, res, next) => {
    console.error('Security Error:', err);

    // Don't leak error details in production
    const isDevelopment = process.env.NODE_ENV !== 'production';
    
    res.status(err.status || 500).json({
        success: false,
        error: {
            code: err.code || 'INTERNAL_ERROR',
            message: isDevelopment ? err.message : 'An error occurred',
            ...(isDevelopment && { stack: err.stack })
        }
    });
};

module.exports = {
    csrfProtection,
    getCSRFToken,
    validateRequest,
    apiLimiter,
    strictApiLimiter,
    securityHeaders,
    sanitizeInput,
    errorHandler
};
