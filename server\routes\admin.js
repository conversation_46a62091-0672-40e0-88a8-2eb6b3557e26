const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');

// Admin email whitelist
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

// Middleware to check admin privileges
const requireAdmin = async (req, res, next) => {
    try {
        const user = await User.findById(req.user.id);
        if (!user || !ADMIN_EMAILS.includes(user.email)) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'ACCESS_DENIED',
                    message: 'Admin privileges required'
                }
            });
        }
        next();
    } catch (error) {
        console.error('Admin auth error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Internal server error'
            }
        });
    }
};

// Get dashboard statistics
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const stats = await User.getStats();
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Get stats error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to get statistics'
            }
        });
    }
});

// Get all users
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const users = await User.getAllUsers();
        
        res.json({
            success: true,
            data: {
                users: users,
                total: users.length
            }
        });
    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to get users'
            }
        });
    }
});

// Get user by ID
router.get('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const user = await User.findById(userId);
        
        if (!user) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: 'User not found'
                }
            });
        }
        
        res.json({
            success: true,
            data: { user }
        });
    } catch (error) {
        console.error('Get user error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to get user'
            }
        });
    }
});

// Delete user
router.delete('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        
        // Prevent admin from deleting themselves
        if (userId === req.user.id) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'CANNOT_DELETE_SELF',
                    message: 'Cannot delete your own account'
                }
            });
        }
        
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: 'User not found'
                }
            });
        }
        
        // Prevent deleting other admins
        if (ADMIN_EMAILS.includes(user.email)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'CANNOT_DELETE_ADMIN',
                    message: 'Cannot delete admin accounts'
                }
            });
        }
        
        await User.deleteUser(userId);
        
        res.json({
            success: true,
            data: {
                message: 'User deleted successfully'
            }
        });
    } catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to delete user'
            }
        });
    }
});

// Get recent activity
router.get('/activity', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const activities = await User.getRecentActivity();
        
        res.json({
            success: true,
            data: {
                activities: activities
            }
        });
    } catch (error) {
        console.error('Get activity error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to get activity'
            }
        });
    }
});

// Update user status
router.patch('/users/:id/status', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const { isActive } = req.body;
        
        if (typeof isActive !== 'boolean') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_STATUS',
                    message: 'Status must be boolean'
                }
            });
        }
        
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: 'User not found'
                }
            });
        }
        
        await User.updateUserStatus(userId, isActive);
        
        res.json({
            success: true,
            data: {
                message: 'User status updated successfully'
            }
        });
    } catch (error) {
        console.error('Update user status error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to update user status'
            }
        });
    }
});

// Get user bookings
router.get('/users/:id/bookings', authenticateToken, requireAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const bookings = await User.getUserBookings(userId);
        
        res.json({
            success: true,
            data: {
                bookings: bookings
            }
        });
    } catch (error) {
        console.error('Get user bookings error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Failed to get user bookings'
            }
        });
    }
});

module.exports = router;
