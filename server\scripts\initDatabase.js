const database = require('../config/database');

async function initializeDatabase() {
    try {
        console.log('Initializing database...');
        
        // Connect to database
        await database.connect();
        
        // Initialize schema
        await database.initializeSchema();
        
        // Insert default data
        await database.insertDefaultData();
        
        console.log('Database initialization completed successfully!');
        
    } catch (error) {
        console.error('Database initialization failed:', error);
        process.exit(1);
    } finally {
        await database.close();
    }
}

// Run if this script is executed directly
if (require.main === module) {
    initializeDatabase();
}

module.exports = initializeDatabase;
