/**
 * End-to-End Tests
 * Tests against the running server to validate complete functionality
 */

const request = require('supertest');

const BASE_URL = 'http://localhost:3001';

describe('End-to-End Tests', () => {
    let agent;
    let csrfToken;
    let authToken;
    
    beforeAll(async () => {
        agent = request.agent(BASE_URL);
        
        // Get CSRF token
        try {
            const csrfResponse = await agent.get('/api/csrf-token');
            if (csrfResponse.status === 200 && csrfResponse.body.success) {
                csrfToken = csrfResponse.body.data.csrfToken;
            }
        } catch (error) {
            console.warn('Could not get CSRF token, server might not be running');
        }
    });
    
    describe('Server Health', () => {
        test('should respond to health check', async () => {
            const response = await request(BASE_URL)
                .get('/api/health')
                .expect(200);
            
            expect(response.body.status).toBe('OK');
            expect(response.body.timestamp).toBeDefined();
        });
        
        test('should serve static files', async () => {
            const response = await request(BASE_URL)
                .get('/')
                .expect(200);
            
            expect(response.text).toContain('html');
        });
        
        test('should serve booking page', async () => {
            const response = await request(BASE_URL)
                .get('/booking.html')
                .expect(200);
            
            expect(response.text).toContain('Book Your Perfect Game');
        });
        
        test('should serve login page', async () => {
            const response = await request(BASE_URL)
                .get('/login.html')
                .expect(200);
            
            expect(response.text).toContain('Login');
        });
    });
    
    describe('Security Headers', () => {
        test('should include security headers', async () => {
            const response = await request(BASE_URL)
                .get('/api/health')
                .expect(200);
            
            expect(response.headers['x-content-type-options']).toBe('nosniff');
            expect(response.headers['x-frame-options']).toBe('DENY');
            expect(response.headers['x-xss-protection']).toBe('1; mode=block');
        });
        
        test('should not expose server information', async () => {
            const response = await request(BASE_URL)
                .get('/api/health')
                .expect(200);
            
            expect(response.headers['x-powered-by']).toBeUndefined();
        });
    });
    
    describe('CSRF Protection', () => {
        test('should provide CSRF token', async () => {
            const response = await agent
                .get('/api/csrf-token')
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.csrfToken).toBeDefined();
            expect(typeof response.body.data.csrfToken).toBe('string');
        });
        
        test('should reject POST without CSRF token', async () => {
            const response = await agent
                .post('/api/auth/login')
                .send({ email: '<EMAIL>', password: 'password' })
                .expect(403);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('CSRF_TOKEN_INVALID');
        });
    });
    
    describe('Authentication API', () => {
        const testEmail = `test-${Date.now()}@example.com`;
        const testPassword = 'TestPassword123!';
        
        test('should signup new user', async () => {
            if (!csrfToken) {
                console.warn('Skipping test - no CSRF token available');
                return;
            }
            
            const userData = {
                firstName: 'Test',
                lastName: 'User',
                email: testEmail,
                password: testPassword,
                phone: '+356 1234 5678'
            };
            
            const response = await agent
                .post('/api/auth/signup')
                .set('X-CSRF-Token', csrfToken)
                .send(userData)
                .expect(201);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(testEmail);
            expect(response.body.data.token).toBeDefined();
            
            authToken = response.body.data.token;
        });
        
        test('should login existing user', async () => {
            if (!csrfToken) {
                console.warn('Skipping test - no CSRF token available');
                return;
            }
            
            const response = await agent
                .post('/api/auth/login')
                .set('X-CSRF-Token', csrfToken)
                .send({
                    email: testEmail,
                    password: testPassword
                })
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(testEmail);
            expect(response.body.data.token).toBeDefined();
            
            authToken = response.body.data.token;
        });
        
        test('should verify token', async () => {
            if (!authToken) {
                console.warn('Skipping test - no auth token available');
                return;
            }
            
            const response = await agent
                .get('/api/auth/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.user.email).toBe(testEmail);
        });
    });
    
    describe('Booking API', () => {
        test('should get facilities', async () => {
            if (!authToken) {
                console.warn('Skipping test - no auth token available');
                return;
            }
            
            const response = await agent
                .get('/api/bookings/facilities')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.facilities).toBeDefined();
            expect(Array.isArray(response.body.data.facilities)).toBe(true);
        });
        
        test('should require authentication for bookings', async () => {
            const response = await agent
                .get('/api/bookings')
                .expect(401);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('NO_TOKEN');
        });
        
        test('should get user bookings when authenticated', async () => {
            if (!authToken) {
                console.warn('Skipping test - no auth token available');
                return;
            }
            
            const response = await agent
                .get('/api/bookings')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);
            
            expect(response.body.success).toBe(true);
            expect(response.body.data.bookings).toBeDefined();
            expect(Array.isArray(response.body.data.bookings)).toBe(true);
        });
    });
    
    describe('Rate Limiting', () => {
        test('should apply rate limiting to API endpoints', async () => {
            // Make multiple requests quickly
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(
                    request(BASE_URL)
                        .get('/api/health')
                );
            }
            
            const responses = await Promise.all(promises);
            
            // All should succeed (health endpoint has higher limits)
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            
            // Check for rate limit headers
            expect(responses[0].headers['x-ratelimit-limit']).toBeDefined();
            expect(responses[0].headers['x-ratelimit-remaining']).toBeDefined();
        });
    });
    
    describe('Input Validation', () => {
        test('should reject malicious input', async () => {
            if (!csrfToken) {
                console.warn('Skipping test - no CSRF token available');
                return;
            }
            
            const maliciousData = {
                firstName: '<script>alert("xss")</script>',
                lastName: 'User',
                email: '<EMAIL>',
                password: 'password'
            };
            
            const response = await agent
                .post('/api/auth/signup')
                .set('X-CSRF-Token', csrfToken)
                .send(maliciousData)
                .expect(400);
            
            expect(response.body.success).toBe(false);
            expect(response.body.error.code).toBe('INVALID_INPUT');
        });
    });
    
    describe('Error Handling', () => {
        test('should handle 404 for non-existent API endpoints', async () => {
            const response = await request(BASE_URL)
                .get('/api/non-existent')
                .expect(404);
        });
        
        test('should handle invalid JSON', async () => {
            const response = await request(BASE_URL)
                .post('/api/auth/login')
                .set('Content-Type', 'application/json')
                .send('invalid json')
                .expect(400);
        });
    });
});
