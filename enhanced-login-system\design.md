# Design Document

## Overview

This design enhances the existing login and registration system by improving visual feedback, database integration, password validation, user flow, and admin functionality. The system builds upon the current Express.js backend with SQLite database and vanilla JavaScript frontend, ensuring seamless integration with existing infrastructure.

## Architecture

### Current System Components
- **Frontend**: Vanilla JavaScript with AuthManager class in `js/login.js`
- **Backend**: Express.js server with SQLite database (`server.js`)
- **Database**: SQLite with existing users table and admin interface
- **Authentication**: JWT tokens with bcrypt password hashing

### Enhanced Components
- **Message System**: CSS-based color-coded feedback system
- **Database Logging**: Enhanced user activity tracking
- **Form Validation**: Client and server-side password validation
- **User Flow**: Automated form transitions and redirections
- **Admin Interface**: Enhanced user management at `/admin/users`

## Components and Interfaces

### 1. Frontend Message System

#### CSS Message Classes
```css
.message-container.success {
    background: linear-gradient(135deg, #2ed573, #7bed9f);
    color: white;
}

.message-container.error {
    background: linear-gradient(135deg, #ff4757, #ff6b8b);
    color: white;
}
```

#### JavaScript Message Interface
```javascript
showMessage(message, type) {
    // type: 'success' (green) or 'error' (red)
    // Updates messageContainer with appropriate styling
}
```

### 2. Enhanced AuthManager Class

#### Core Methods Enhancement
- `handleLogin()`: Enhanced with proper success/error messaging and redirection
- `handleRegister()`: Enhanced with email pre-fill and form switching
- `showMessage()`: Color-coded message display system
- `validatePassword()`: Client-side 6-character minimum validation

#### Registration Flow
```javascript
// Registration success flow:
1. Validate all fields (including 6-char password)
2. Submit to /api/register
3. Show green success message
4. Switch to login form after 2 seconds
5. Pre-fill email field
6. Focus password field
```

#### Login Flow
```javascript
// Login success flow:
1. Validate credentials
2. Submit to /api/login
3. Store token and user data
4. Show green success message
5. Redirect to index.html after 1.5 seconds
```

### 3. Backend API Enhancements

#### Enhanced Registration Endpoint (`/api/register`)
```javascript
// Current validation enhanced with:
- 6-character minimum password validation
- Improved error messages
- Database logging of registration attempts
- Success response formatting
```

#### Enhanced Login Endpoint (`/api/login`)
```javascript
// Current functionality enhanced with:
- Login attempt logging (success/failure)
- Improved error messages
- Consistent response formatting
```

#### New Login Logging Table
```sql
CREATE TABLE login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Database Schema Enhancements

#### Existing Users Table (No Changes Required)
- Already has all necessary fields
- `registration_date`, `last_login` fields available
- Admin interface already functional at `/admin/users`

#### New Login Attempts Tracking
```sql
-- Track all login attempts for admin monitoring
INSERT INTO login_attempts (email, success, ip_address, user_agent) 
VALUES (?, ?, ?, ?);
```

### 5. Admin Interface Enhancement

#### Current Admin Panel (`/admin/users`)
- Already displays all registered users
- Shows registration dates and login status
- No changes required - meets requirements

#### Additional Admin Features (Optional)
- Login attempts log view
- User activity statistics
- Failed login attempt monitoring

## Data Models

### User Registration Data Flow
```javascript
// Client -> Server
{
    name: string,
    email: string,
    password: string,
    confirmPassword: string
}

// Server -> Database
{
    name: trimmed string,
    email: lowercase string,
    password: bcrypt hashed,
    registration_date: CURRENT_TIMESTAMP
}

// Server -> Client (Success)
{
    success: true,
    message: "Account created successfully!",
    userId: number
}
```

### User Login Data Flow
```javascript
// Client -> Server
{
    email: string,
    password: string,
    rememberMe: boolean
}

// Server -> Client (Success)
{
    success: true,
    token: JWT_string,
    user: {
        id: number,
        name: string,
        email: string
    }
}
```

### Login Attempt Logging
```javascript
// Server -> Database (All Attempts)
{
    email: string,
    success: boolean,
    ip_address: string,
    user_agent: string,
    timestamp: CURRENT_TIMESTAMP
}
```

## Error Handling

### Client-Side Validation Errors
- Empty fields: "Please fill in all required fields"
- Password too short: "Password must be at least 6 characters long"
- Password mismatch: "Passwords do not match"
- Invalid email: "Please enter a valid email address"

### Server-Side Response Errors
- Duplicate email: "An account with this email already exists"
- Invalid credentials: "Invalid email or password"
- Network issues: "Network error. Please check your connection and try again"
- Server errors: "An error occurred. Please try again"

### Error Display System
```javascript
// All errors displayed with red styling
showMessage(errorMessage, 'error');
// Applies .message-container.error CSS class
```

## Testing Strategy

### Unit Tests
- Password validation (6-character minimum)
- Email validation
- Form switching functionality
- Message display system

### Integration Tests
- Registration -> Login flow
- Database user creation
- Login attempt logging
- Admin interface user display

### End-to-End Tests
- Complete registration process
- Login with new account
- Redirection to index.html
- Admin panel user verification

### Manual Testing Scenarios
1. **Registration Flow**:
   - Register new user
   - Verify green success message
   - Confirm automatic switch to login
   - Check email pre-fill
   - Verify user in admin panel

2. **Login Flow**:
   - Login with new account
   - Verify green success message
   - Confirm redirection to index.html
   - Check token storage

3. **Error Scenarios**:
   - Test password < 6 characters
   - Test password mismatch
   - Test duplicate email
   - Test invalid login
   - Verify red error messages

### Database Testing
- Verify user creation in database
- Check login attempt logging
- Confirm admin interface displays users
- Test user data integrity

## Implementation Notes

### CSS Requirements
- Green success messages: `#2ed573` to `#7bed9f` gradient
- Red error messages: `#ff4757` to `#ff6b8b` gradient
- Smooth transitions and proper contrast

### JavaScript Enhancements
- Clean up duplicate code in `login.js`
- Fix script path reference in HTML
- Implement proper error handling
- Add form state management

### Backend Modifications
- Add login attempt logging
- Enhance error messages
- Ensure consistent API responses
- Maintain existing security measures

### Database Considerations
- Login attempts table creation
- Index on email and timestamp fields
- Data retention policies for logs
- Admin query optimization

This design maintains compatibility with the existing system while adding the requested enhancements for better user experience and administrative oversight.